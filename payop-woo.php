<?php
/**
 * Plugin Name: PayOp for WooCommerce
 * Plugin URI: https://github.com/payop/payop-woocommerce
 * Description: Accept payments through PayOp payment aggregator with grouped payment methods and direct gateway redirection.
 * Version: 1.0.0
 * Author: PayOp
 * Author URI: https://payop.com
 * Text Domain: payop-woo
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 8.2
 * WC requires at least: 8.0
 * WC tested up to: 8.5
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PAYOP_VERSION', '1.0.0');
define('PAYOP_PLUGIN_FILE', __FILE__);
define('PAYOP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PAYOP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PAYOP_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo __('PayOp for WooCommerce requires WooCommerce to be installed and active.', 'payop-woo');
        echo '</p></div>';
    });
    return;
}

// Check PHP version
if (version_compare(PHP_VERSION, '8.2', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo sprintf(__('PayOp for WooCommerce requires PHP 8.2 or higher. You are running PHP %s.', 'payop-woo'), PHP_VERSION);
        echo '</p></div>';
    });
    return;
}

// Autoloader
spl_autoload_register(function ($class) {
    $prefix = 'Payop\\';
    $base_dir = PAYOP_PLUGIN_DIR . 'includes/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

/**
 * Main PayOp Plugin Class
 */
final class Payop_WooCommerce {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', [$this, 'init'], 0);
        add_action('plugins_loaded', [$this, 'init_plugin'], 10);
        
        // Activation and deactivation hooks
        register_activation_hook(PAYOP_PLUGIN_FILE, [$this, 'activate']);
        register_deactivation_hook(PAYOP_PLUGIN_FILE, [$this, 'deactivate']);
        
        // Uninstall hook
        register_uninstall_hook(PAYOP_PLUGIN_FILE, [__CLASS__, 'uninstall']);
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('payop-woo', false, dirname(PAYOP_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Initialize plugin after all plugins loaded
     */
    public function init_plugin() {
        if (!class_exists('WooCommerce')) {
            return;
        }
        
        // Initialize core classes
        $this->init_classes();
        
        // Initialize payment gateways
        add_filter('woocommerce_payment_gateways', [$this, 'add_payment_gateways']);
        
        // Initialize admin
        if (is_admin()) {
            new \Payop\Admin\Admin();
        }
        
        // Initialize webhook handler
        new \Payop\Webhook\Handler();
        
        // Initialize blocks integration
        if (class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
            add_action('woocommerce_blocks_payment_method_type_registration', [$this, 'register_payment_method_blocks']);
        }
    }
    
    /**
     * Initialize core classes
     */
    private function init_classes() {
        // Initialize API client
        \Payop\API\Client::instance();
        
        // Initialize payment method manager
        \Payop\Payment\MethodManager::instance();
        
        // Initialize group manager
        \Payop\Payment\GroupManager::instance();
    }
    
    /**
     * Add payment gateways
     */
    public function add_payment_gateways($gateways) {
        $payment_manager = \Payop\Payment\MethodManager::instance();
        $enabled_gateways = $payment_manager->get_enabled_gateways();
        
        return array_merge($gateways, $enabled_gateways);
    }
    
    /**
     * Register payment method blocks
     */
    public function register_payment_method_blocks($payment_method_registry) {
        $blocks_integration = new \Payop\Blocks\Integration();
        $blocks_integration->register_payment_methods($payment_method_registry);
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Schedule sync job
        if (!wp_next_scheduled('payop_sync_payment_methods')) {
            wp_schedule_event(time(), 'daily', 'payop_sync_payment_methods');
        }
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('payop_sync_payment_methods');
        
        // Clear cache
        wp_cache_flush();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables if option is set
        if (get_option('payop_remove_data_on_uninstall', false)) {
            self::drop_tables();
        }
        
        // Remove options
        self::remove_options();
        
        // Clear cache
        wp_cache_flush();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        require_once PAYOP_PLUGIN_DIR . 'includes/Database/Schema.php';
        \Payop\Database\Schema::create_tables();
    }
    
    /**
     * Drop database tables
     */
    private static function drop_tables() {
        require_once PAYOP_PLUGIN_DIR . 'includes/Database/Schema.php';
        \Payop\Database\Schema::drop_tables();
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $defaults = [
            'payop_environment' => 'sandbox',
            'payop_grouping_strategy' => 'type',
            'payop_cache_duration' => 3600,
            'payop_webhook_enabled' => true,
            'payop_remove_data_on_uninstall' => false,
        ];
        
        foreach ($defaults as $option => $value) {
            if (get_option($option) === false) {
                add_option($option, $value);
            }
        }
    }
    
    /**
     * Remove plugin options
     */
    private static function remove_options() {
        $options = [
            'payop_environment',
            'payop_public_key',
            'payop_secret_key',
            'payop_jwt_token',
            'payop_application_id',
            'payop_grouping_strategy',
            'payop_enabled_groups',
            'payop_enabled_standalone_methods',
            'payop_cache_duration',
            'payop_webhook_enabled',
            'payop_remove_data_on_uninstall',
        ];
        
        foreach ($options as $option) {
            delete_option($option);
        }
        
        // Remove transients
        delete_transient('payop_payment_methods_606');
    }
}

// Initialize plugin
Payop_WooCommerce::instance();
