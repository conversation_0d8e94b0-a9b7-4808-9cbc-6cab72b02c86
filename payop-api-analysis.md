# PayOp API Analysis for WooCommerce Plugin Development

## Overview
This document provides a comprehensive analysis of the PayOp API for developing a WooCommerce payment plugin that bypasses PayOp's hosted checkout page and provides individual payment methods as separate options.

## Test Credentials
- **Public Key**: application-606
- **Project ID**: 606
- **Secret Key**: fd6d7b9d6e14146ba064cd3b7afd7a0e
- **JWT Token**: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTUzNCwidGltZSI6MTc1MDUxOTkyOSwidHdvRmFjdG9yIjp7InBhc3NlZCI6dHJ1ZX0sInRva2VuSWQiOjYyNDMsImV4cGlyZWRBdCI6MTc1MTMxNzE5OSwicm9sZSI6MSwiYWNjZXNzVG9rZW4iOiJiNzIyMzY4Mjc0M2U3YzYzNjYzZDU1MTUifQ.CfbiRB9f7OoTSpAzjOfpx_L9dUmkDGBAT1ZixzQe-fc

## API Integration Types

PayOp offers two integration methods:

### 1. Hosted Page Integration (Simple)
- User redirected to PayOp's checkout page
- PayOp handles all payment method selection and processing
- Minimal development effort required

### 2. Direct Integration (Advanced) - **Our Target**
- Merchant collects payment method-specific data
- Direct redirection to payment gateway
- Full control over checkout experience
- Ideal for our WooCommerce plugin requirements

## Core API Endpoints Analysis

### 1. Get Available Payment Methods
**Endpoint**: `GET /v1/instrument-settings/payment-methods/available-for-application/{APPLICATION_ID}`
**Authentication**: Bearer JWT Token required
**Purpose**: Retrieve all available payment methods with their required fields

### 2. Create Invoice
**Endpoint**: `POST /v1/invoices/create`
**Authentication**: Public key + signature
**Purpose**: Create payment invoice before processing

### 3. Create Checkout Transaction
**Endpoint**: `POST /v1/checkout/create`
**Authentication**: Bearer JWT Token required
**Purpose**: Process payment with collected customer data

### 4. Check Invoice Status
**Endpoint**: `GET /v1/checkout/check-invoice-status/{invoiceID}`
**Purpose**: Poll for payment status updates

## API Testing Results

### Test 1: Get Available Payment Methods
**Status**: ✅ SUCCESS
**Endpoint**: `GET /v1/instrument-settings/payment-methods/available-for-application/606`
**Authentication**: Bearer JWT Token

**Key Findings**:
- **Total Payment Methods**: 100+ individual payment methods available
- **Payment Types**:
  - `bank_transfer` - Bank transfers and online banking
  - `cash` - Cash payment locations
  - `ewallet` - Digital wallets
  - `cards_international` - Credit/debit cards
  - `crypto` - Cryptocurrency payments

**Sample Payment Methods Analyzed**:

1. **Cards via PayDo** (ID: 700001)
   - Type: `cards_international`
   - Currencies: EUR, AUD, CAD, GBP, USD, DKK
   - Countries: 100+ countries supported
   - Required Fields: email, name

2. **Pay by bank** (ID: 200018)
   - Type: `bank_transfer`
   - Currencies: EUR
   - Countries: AT, ES, IT, PT, FR, DE, FI, NL, EE, LT, IE, PL
   - Required Fields: email, name, date_of_birth, bank_code, bank_type, bank_country

3. **Crypto Payment** (ID: 6110)
   - Type: `crypto`
   - Currencies: EUR
   - Countries: Global support (100+ countries)
   - Required Fields: email, name

4. **PIX** (ID: 200969)
   - Type: `ewallet`
   - Currencies: EUR
   - Countries: BR
   - Required Fields: email, name, cpf_cnpj

**Field Types Discovered**:
- `email` - Email validation
- `text` - General text input
- `bank_code` - Bank identifier codes
- `bank_type` - Transfer type (SEPA, SEPA_INSTANT, FPS)
- `bank_country` - Country codes
- `iban_type` - IBAN classification
- Custom fields with regex validation (e.g., CPF/CNPJ, document numbers)

**Critical Insight**: Each payment method has its own identifier, supported currencies, countries, and required field configuration. This confirms that individual payment methods can be implemented as separate WooCommerce payment gateways.

### Test 2: Create Invoice
**Status**: ✅ SUCCESS
**Endpoint**: `POST /v1/invoices/create`
**Authentication**: Public key + SHA-256 signature

**Test Data**:
- Amount: 10.00 EUR
- Order ID: test-order-123
- Signature: b3b4b85711da540d9edb74018119a24081fd3ee020352aeeea8a1050a8d10600

**Response**:
```json
{
  "data": "deb2bef3-ac51-4a23-9414-bfdc2c6b3edc",
  "status": 1
}
```

**Key Findings**:
- Invoice creation successful
- Returns unique invoice identifier
- Signature generation works: `amount:currency:id:secretKey` hashed with SHA-256
- Required fields: publicKey, order (id, amount, currency, items), signature, payer.email

**IMPORTANT DISCOVERY**: Based on the prompt requirements, we should NOT use the `/v1/checkout/create` endpoint as this leads to PayOp's checkout system. Instead, we need to focus on **direct gateway redirection** after invoice creation.

## Critical Realization: Direct Gateway Redirection Approach

After re-reading the requirements, the goal is to:
1. Create invoice via PayOp API
2. Collect payment method-specific data on WooCommerce checkout
3. **Redirect directly to the end payment gateway** (bypassing PayOp's hosted checkout)

This means we need to investigate how to get direct gateway URLs from PayOp API, not use their checkout system.

### Test 3: Create Invoice with Specific Payment Method
**Status**: ✅ SUCCESS
**Endpoint**: `POST /v1/invoices/create`
**Payment Method**: 200018 (Pay by bank)

**Test Data**:
- Amount: 10.00 EUR
- Order ID: test-order-456
- Payment Method: 200018
- Signature: cb589668d149556c943a5b0e9eba4e9877a4decf13c5f18de1d103182d663af8

**Response**:
```json
{
  "data": "2c47e3e2-f14a-495a-a264-4b30e23d9192",
  "status": 1
}
```

**Key Finding**: When a specific payment method is included in invoice creation, it works successfully.

### Test 4: Get Invoice Details with Payment Method
**Status**: ✅ SUCCESS
**Endpoint**: `GET /v1/invoices/2c47e3e2-f14a-495a-a264-4b30e23d9192`

**Critical Discovery**: The invoice response includes the complete payment method configuration:
```json
{
  "data": {
    "identifier": "2c47e3e2-f14a-495a-a264-4b30e23d9192",
    "status": 0,
    "paymentMethod": {
      "identifier": 200018,
      "fields": [
        {"name": "email", "type": "email", "required": true},
        {"name": "name", "type": "text", "title": "Full name", "required": true},
        {"name": "date_of_birth", "type": "text", "title": "Date of birth (DD.MM.YYYY)", "required": true},
        {"name": "bank_code", "type": "bank_code", "required": true},
        {"name": "bank_type", "type": "bank_type", "regexp": "^(SEPA|SEPA_INSTANT)$", "required": true},
        {"name": "bank_country", "type": "bank_country", "regexp": "^(ES|IT|PT|FR|DE|FI|NL|EE|LT|AT|PL|IE)$", "required": true}
      ]
    }
  }
}
```

**BREAKTHROUGH**: This confirms that we can:
1. Create invoices with specific payment methods
2. Get the exact field requirements for each payment method
3. Build WooCommerce checkout forms with method-specific fields

### Test 5: Direct Invoice Preprocessing URL
**Status**: ✅ SUCCESS
**URL**: `https://checkout.payop.com/en/payment/invoice-preprocessing/2c47e3e2-f14a-495a-a264-4b30e23d9192`
**Response**: HTTP 200 (Accessible)

**Key Finding**: The invoice preprocessing URL is directly accessible, confirming we can redirect users to PayOp's payment processing page.

## PayOp Integration Architecture Analysis

### Understanding PayOp's Role
PayOp functions as a **payment aggregator** that provides access to multiple payment gateways through a unified API. Even with "direct integration," PayOp still handles the final payment processing and gateway communication.

### Possible Integration Approaches

#### Approach 1: Invoice Preprocessing Redirection (Recommended)
**Flow**:
1. User selects payment method on WooCommerce checkout
2. Collect payment method-specific fields in WooCommerce
3. Create PayOp invoice with specific payment method
4. Redirect user to: `https://checkout.payop.com/{locale}/payment/invoice-preprocessing/{invoiceId}`
5. PayOp handles gateway redirection and payment processing
6. User returns to WooCommerce success/fail pages
7. IPN webhook updates order status

**Advantages**:
- ✅ Bypasses PayOp's payment method selection page
- ✅ Collects data on WooCommerce side
- ✅ Direct redirection to payment processing
- ✅ Maintains PayOp's security and compliance
- ✅ Supports all payment methods

#### Approach 2: Checkout API + Status Polling (Complex)
**Flow**:
1. Create invoice
2. Create checkout transaction via API
3. Poll status endpoint for redirection URL
4. Redirect to payment gateway

**Disadvantages**:
- ❌ More complex implementation
- ❌ Requires polling mechanism
- ❌ Still goes through PayOp system
- ❌ No significant advantage over Approach 1

## Payment Method Categories Analysis

Based on the API response, PayOp supports 100+ payment methods across these categories:

### 1. Bank Transfers (`bank_transfer`)
**Examples**:
- Pay by bank (ID: 200018) - Multi-country SEPA
- Pay by bank in Germany (ID: 200017)
- Pay by bank in UK (ID: ********)
- Bancolombia - PSE (ID: 632)

**Common Fields**: email, name, date_of_birth, bank_code, bank_type, bank_country

### 2. Credit/Debit Cards (`cards_international`)
**Examples**:
- Cards via PayDo (ID: 700001)

**Common Fields**: email, name

### 3. Digital Wallets (`ewallet`)
**Examples**:
- PIX (ID: 200969) - Brazil
- PaydoEWallet (ID: 846)
- GCash Bills Pay (ID: 862) - Philippines

**Common Fields**: email, name, country-specific fields (e.g., CPF/CNPJ for Brazil)

### 4. Cash Payment Locations (`cash`)
**Examples**:
- Oxxo via PayDo (ID: 692) - Mexico
- Efecty (ID: 705) - Colombia
- CashToCode (ID: 2001017) - Europe

**Common Fields**: email, name, phone, document numbers

### 5. Cryptocurrency (`crypto`)
**Examples**:
- Crypto Payment (ID: 6110)

**Common Fields**: email, name

## WooCommerce Integration Strategy

### Plugin Architecture
```
payop-woo/
├── payop-woo.php (Main plugin file)
├── includes/
│   ├── class-payop-gateway-factory.php
│   ├── class-payop-api-client.php
│   ├── class-payop-webhook-handler.php
│   └── gateways/
│       ├── class-payop-bank-transfer.php
│       ├── class-payop-cards.php
│       ├── class-payop-ewallet.php
│       ├── class-payop-cash.php
│       └── class-payop-crypto.php
├── admin/
│   ├── class-payop-admin.php
│   └── views/
└── assets/
    ├── css/
    └── js/

### Individual Payment Gateway Implementation

Each payment method will be implemented as a separate WooCommerce payment gateway:

#### Gateway Registration
```php
// Register each payment method as individual gateway
add_filter('woocommerce_payment_gateways', function($gateways) {
    $gateways[] = 'WC_Payop_Bank_Transfer_Gateway';
    $gateways[] = 'WC_Payop_Cards_Gateway';
    $gateways[] = 'WC_Payop_PIX_Gateway';
    // ... etc for each enabled method
    return $gateways;
});
```

#### Dynamic Field Generation
```php
public function payment_fields() {
    // Get payment method config from PayOp API
    $config = $this->get_payment_method_config();

    // Generate form fields based on config
    foreach ($config['fields'] as $field) {
        $this->render_field($field);
    }
}
```

#### Payment Processing Flow
```php
public function process_payment($order_id) {
    // 1. Collect form data
    $payment_data = $this->get_payment_form_data();

    // 2. Create PayOp invoice
    $invoice = $this->create_payop_invoice($order_id, $payment_data);

    // 3. Redirect to PayOp preprocessing
    return array(
        'result' => 'success',
        'redirect' => "https://checkout.payop.com/en/payment/invoice-preprocessing/{$invoice['id']}"
    );
}
```

### Block Checkout Compatibility

For WooCommerce block checkout, each payment method needs:

#### Block Registration
```javascript
// Register payment method block
registerPaymentMethod({
    name: 'payop_bank_transfer',
    label: 'Bank Transfer',
    content: <PayopBankTransferForm />,
    edit: <PayopBankTransferForm />,
    canMakePayment: () => true,
    ariaLabel: 'Bank Transfer Payment Method',
});
```

#### React Components
```jsx
const PayopBankTransferForm = () => {
    const [formData, setFormData] = useState({});

    return (
        <div className="payop-payment-form">
            {/* Render dynamic fields based on payment method config */}
        </div>
    );
};
```

## Security & Validation Requirements

### API Security
- **Signature Generation**: SHA-256 hash of `amount:currency:order_id:secret_key`
- **JWT Authentication**: Required for payment methods API
- **IP Whitelisting**: For IPN webhooks (*************, *************, ************, *************)

### Data Validation
- **Field Validation**: Use regex patterns from PayOp API response
- **Currency Support**: Validate against payment method supported currencies
- **Country Restrictions**: Check payment method country limitations

### HPOS Compatibility
- Use `wc_get_order()` instead of direct post access
- Implement custom order data storage for PayOp transaction IDs
- Support order meta data in HPOS format

## IPN Webhook Implementation

### Webhook Handler
```php
class Payop_Webhook_Handler {
    public function handle_ipn() {
        // 1. Validate IP address
        // 2. Parse IPN payload
        // 3. Update order status
        // 4. Return HTTP 200
    }
}
```

### IPN Payload Structure
```json
{
  "invoice": {
    "id": "invoice_id",
    "status": 1,
    "txid": "transaction_id"
  },
  "transaction": {
    "id": "transaction_id",
    "state": 2,
    "order": {"id": "ORDER_ID"}
  }
}
```

## Testing Framework

### cURL Test Commands

#### 1. Get Payment Methods
```bash
curl -X GET "https://api.payop.com/v1/instrument-settings/payment-methods/available-for-application/606" \
 -H "Authorization: Bearer JWT_TOKEN"
```

#### 2. Create Invoice
```bash
curl -X POST "https://api.payop.com/v1/invoices/create" \
 -H "Content-Type: application/json" \
 -d '{
   "publicKey": "application-606",
   "order": {"id": "test", "amount": "10.00", "currency": "EUR", "items": []},
   "signature": "GENERATED_SIGNATURE",
   "payer": {"email": "<EMAIL>"},
   "paymentMethod": 200018
 }'
```

#### 3. Test Redirection
```bash
curl -I "https://checkout.payop.com/en/payment/invoice-preprocessing/INVOICE_ID"
```

## Development Roadmap

### Phase 1: Core Infrastructure (Week 1-2)
- [ ] Set up plugin structure
- [ ] Implement PayOp API client
- [ ] Create signature generation utility
- [ ] Build admin settings panel
- [ ] Implement webhook handler

### Phase 2: Payment Gateway Framework (Week 3-4)
- [ ] Create base payment gateway class
- [ ] Implement dynamic field generation
- [ ] Build payment method factory
- [ ] Add HPOS compatibility
- [ ] Create admin payment method management

### Phase 3: Individual Payment Methods (Week 5-8)
- [ ] Implement bank transfer gateways
- [ ] Add card payment gateway
- [ ] Create e-wallet gateways
- [ ] Implement cash payment gateways
- [ ] Add cryptocurrency gateway

### Phase 4: Block Checkout Integration (Week 9-10)
- [ ] Create React components for each payment method
- [ ] Register payment method blocks
- [ ] Implement block-specific validation
- [ ] Add block checkout styling

### Phase 5: Testing & Optimization (Week 11-12)
- [ ] Comprehensive testing with all payment methods
- [ ] Performance optimization
- [ ] Security audit
- [ ] Documentation completion

## Conclusions & Recommendations

### Key Findings
1. **✅ Direct Gateway Redirection**: Achievable via invoice preprocessing URLs
2. **✅ Individual Payment Methods**: Can be implemented as separate WooCommerce gateways
3. **✅ Dynamic Field Collection**: PayOp API provides field configurations for each method
4. **✅ Block Checkout Compatible**: React components can be built for each payment method
5. **✅ HPOS Ready**: Modern WooCommerce APIs support required functionality

### Technical Feasibility
- **High**: All requirements can be met using PayOp's existing API
- **Approach**: Invoice preprocessing redirection provides optimal user experience
- **Scalability**: Plugin architecture supports easy addition of new payment methods

### Limitations Identified
1. **PayOp Dependency**: Final payment processing still goes through PayOp (by design)
2. **Method Availability**: Some payment methods may not be enabled for test account
3. **Regional Restrictions**: Payment methods have country/currency limitations

### Recommended Implementation
Use **Approach 1 (Invoice Preprocessing Redirection)** as it provides:
- Clean separation of concerns
- Optimal user experience
- Maintainable codebase
- Full PayOp feature support
- WooCommerce best practices compliance

### Next Steps
1. Begin Phase 1 development
2. Set up development environment with test credentials
3. Implement core API client and test with available payment methods
4. Create proof-of-concept for one payment method type
5. Iterate based on testing results

---

# WooCommerce PayOp Plugin Development Plan

## 1. Payment Method Grouping System Design

### 1.1 Grouping Strategy Overview

Based on the 100+ payment methods available through PayOp API, we'll implement a flexible grouping system that allows merchants to organize payment methods for optimal user experience.

#### Primary Grouping Options:

**A. By Payment Type (Default)**
```php
$payment_type_groups = [
    'bank_transfer' => [
        'title' => 'Bank Transfer',
        'description' => 'Pay directly from your bank account',
        'icon' => 'bank-icon.svg',
        'methods' => [200018, 200017, ********, 632, ...] // PayOp method IDs
    ],
    'cards_international' => [
        'title' => 'Credit/Debit Cards',
        'description' => 'Pay with your credit or debit card',
        'icon' => 'card-icon.svg',
        'methods' => [700001, ...]
    ],
    'ewallet' => [
        'title' => 'Digital Wallets',
        'description' => 'Pay with digital wallet services',
        'icon' => 'wallet-icon.svg',
        'methods' => [200969, 846, 862, ...]
    ],
    'cash' => [
        'title' => 'Cash Payments',
        'description' => 'Pay at physical locations',
        'icon' => 'cash-icon.svg',
        'methods' => [692, 705, 2001017, ...]
    ],
    'crypto' => [
        'title' => 'Cryptocurrency',
        'description' => 'Pay with cryptocurrency',
        'icon' => 'crypto-icon.svg',
        'methods' => [6110, ...]
    ]
];
```

**B. By Country/Region**
```php
$country_groups = [
    'europe' => [
        'title' => 'European Payments',
        'countries' => ['DE', 'FR', 'ES', 'IT', 'NL', 'AT', ...],
        'methods' => [200017, 200020, 200021, 200022, ...]
    ],
    'latin_america' => [
        'title' => 'Latin American Payments',
        'countries' => ['BR', 'CO', 'MX', 'PE', 'CL', ...],
        'methods' => [757, 632, 618, 697, ...]
    ],
    'asia_pacific' => [
        'title' => 'Asia Pacific Payments',
        'countries' => ['PH', 'ID', 'SG', 'MY', ...],
        'methods' => [4521, 200672, 4518, ...]
    ]
];
```

**C. By Currency**
```php
$currency_groups = [
    'EUR' => ['title' => 'Euro Payments', 'methods' => [...]],
    'USD' => ['title' => 'US Dollar Payments', 'methods' => [...]],
    'GBP' => ['title' => 'British Pound Payments', 'methods' => [...]],
    'PHP' => ['title' => 'Philippine Peso Payments', 'methods' => [...]]
];
```

### 1.2 Admin Interface Design

#### Payment Method Management Screen
```php
class Payop_Admin_Payment_Methods {

    public function render_management_page() {
        ?>
        <div class="payop-admin-wrapper">
            <h1>PayOp Payment Methods</h1>

            <!-- Grouping Strategy Selection -->
            <div class="grouping-strategy">
                <h2>Grouping Strategy</h2>
                <label>
                    <input type="radio" name="grouping_strategy" value="type" checked>
                    Group by Payment Type (Recommended)
                </label>
                <label>
                    <input type="radio" name="grouping_strategy" value="country">
                    Group by Country/Region
                </label>
                <label>
                    <input type="radio" name="grouping_strategy" value="currency">
                    Group by Currency
                </label>
                <label>
                    <input type="radio" name="grouping_strategy" value="standalone">
                    Show All Methods Individually
                </label>
            </div>

            <!-- Payment Method Groups -->
            <div class="payment-groups">
                <?php $this->render_payment_groups(); ?>
            </div>

            <!-- Ungrouped Methods -->
            <div class="ungrouped-methods">
                <h3>Standalone Payment Methods</h3>
                <?php $this->render_ungrouped_methods(); ?>
            </div>
        </div>
        <?php
    }

    private function render_payment_groups() {
        $groups = $this->get_payment_groups();
        foreach ($groups as $group_id => $group) {
            ?>
            <div class="payment-group" data-group="<?php echo $group_id; ?>">
                <div class="group-header">
                    <h3><?php echo $group['title']; ?></h3>
                    <label class="group-toggle">
                        <input type="checkbox" name="enabled_groups[]" value="<?php echo $group_id; ?>"
                               <?php checked($group['enabled']); ?>>
                        Enable Group
                    </label>
                </div>

                <div class="group-methods">
                    <?php foreach ($group['methods'] as $method): ?>
                        <div class="payment-method-item">
                            <img src="<?php echo $method['logo']; ?>" alt="<?php echo $method['title']; ?>">
                            <span><?php echo $method['title']; ?></span>
                            <label>
                                <input type="checkbox" name="enabled_methods[]"
                                       value="<?php echo $method['identifier']; ?>"
                                       <?php checked($method['enabled']); ?>>
                                Enable
                            </label>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php
        }
    }
}
```

### 1.3 Fallback System for Ungrouped Methods

```php
class Payop_Payment_Method_Manager {

    public function get_checkout_payment_methods() {
        $grouping_strategy = get_option('payop_grouping_strategy', 'type');
        $enabled_groups = get_option('payop_enabled_groups', []);
        $enabled_standalone = get_option('payop_enabled_standalone_methods', []);

        $payment_methods = [];

        // Add grouped payment methods
        if (!empty($enabled_groups)) {
            foreach ($enabled_groups as $group_id) {
                $payment_methods[] = $this->create_group_gateway($group_id);
            }
        }

        // Add standalone payment methods (ungrouped)
        foreach ($enabled_standalone as $method_id) {
            $payment_methods[] = $this->create_individual_gateway($method_id);
        }

        return $payment_methods;
    }

    private function create_group_gateway($group_id) {
        return new WC_Payop_Group_Gateway($group_id);
    }

    private function create_individual_gateway($method_id) {
        $method_config = $this->get_payop_method_config($method_id);
        return new WC_Payop_Individual_Gateway($method_id, $method_config);
    }
}
```

## 2. Dynamic Checkout Implementation

### 2.1 User Flow Design: Group → Method → Fields → Payment

#### Step 1: Group Selection (Checkout Page)
```php
class WC_Payop_Group_Gateway extends WC_Payment_Gateway {

    public function payment_fields() {
        $group_methods = $this->get_group_methods();

        if (count($group_methods) === 1) {
            // Single method in group - skip method selection
            $this->render_payment_fields($group_methods[0]);
        } else {
            // Multiple methods - show method selection
            $this->render_method_selection($group_methods);
        }
    }

    private function render_method_selection($methods) {
        ?>
        <div class="payop-method-selection" data-group="<?php echo $this->id; ?>">
            <p>Choose your preferred payment method:</p>

            <?php foreach ($methods as $method): ?>
                <label class="payop-method-option">
                    <input type="radio" name="payop_selected_method"
                           value="<?php echo $method['identifier']; ?>"
                           data-method-config='<?php echo json_encode($method['config']); ?>'>
                    <div class="method-info">
                        <img src="<?php echo $method['logo']; ?>" alt="<?php echo $method['title']; ?>">
                        <span class="method-title"><?php echo $method['title']; ?></span>
                        <span class="method-description"><?php echo $method['description']; ?></span>
                    </div>
                </label>
            <?php endforeach; ?>

            <!-- Dynamic fields container -->
            <div id="payop-dynamic-fields" style="display: none;"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('input[name="payop_selected_method"]').on('change', function() {
                const methodConfig = JSON.parse($(this).attr('data-method-config'));
                PayopCheckout.renderDynamicFields(methodConfig.fields);
            });
        });
        </script>
        <?php
    }
}
```

#### Step 2: Dynamic Field Generation
```php
class Payop_Field_Renderer {

    public function render_dynamic_fields($fields) {
        $html = '<div class="payop-payment-fields">';

        foreach ($fields as $field) {
            $html .= $this->render_field($field);
        }

        $html .= '</div>';
        return $html;
    }

    private function render_field($field) {
        $field_html = '';
        $required = $field['required'] ? 'required' : '';
        $title = isset($field['title']) ? $field['title'] : ucfirst(str_replace('_', ' ', $field['name']));

        switch ($field['type']) {
            case 'email':
                $field_html = sprintf(
                    '<p class="form-row">
                        <label for="payop_%s">%s %s</label>
                        <input type="email" id="payop_%s" name="payop_%s" %s
                               class="input-text" placeholder="%s">
                    </p>',
                    $field['name'], $title, $required ? '*' : '',
                    $field['name'], $field['name'], $required,
                    $this->get_field_placeholder($field)
                );
                break;

            case 'text':
                $pattern = isset($field['regexp']) ? 'pattern="' . $field['regexp'] . '"' : '';
                $field_html = sprintf(
                    '<p class="form-row">
                        <label for="payop_%s">%s %s</label>
                        <input type="text" id="payop_%s" name="payop_%s" %s %s
                               class="input-text" placeholder="%s">
                    </p>',
                    $field['name'], $title, $required ? '*' : '',
                    $field['name'], $field['name'], $required, $pattern,
                    $this->get_field_placeholder($field)
                );
                break;

            case 'bank_code':
                $field_html = $this->render_bank_code_field($field);
                break;

            case 'bank_type':
                $field_html = $this->render_bank_type_field($field);
                break;

            case 'bank_country':
                $field_html = $this->render_bank_country_field($field);
                break;

            case 'iban_type':
                $field_html = $this->render_iban_type_field($field);
                break;
        }

        return $field_html;
    }

    private function render_bank_code_field($field) {
        // Fetch available bank codes from PayOp or use predefined list
        $bank_codes = $this->get_bank_codes();

        $html = '<p class="form-row">';
        $html .= '<label for="payop_bank_code">Select Bank *</label>';
        $html .= '<select id="payop_bank_code" name="payop_bank_code" required class="select">';
        $html .= '<option value="">Choose your bank...</option>';

        foreach ($bank_codes as $code => $name) {
            $html .= sprintf('<option value="%s">%s</option>', $code, $name);
        }

        $html .= '</select></p>';
        return $html;
    }

    private function render_bank_type_field($field) {
        $types = ['SEPA' => 'SEPA Transfer', 'SEPA_INSTANT' => 'SEPA Instant Transfer', 'FPS' => 'Faster Payments'];

        $html = '<p class="form-row">';
        $html .= '<label for="payop_bank_type">Transfer Type *</label>';
        $html .= '<select id="payop_bank_type" name="payop_bank_type" required class="select">';

        foreach ($types as $value => $label) {
            $html .= sprintf('<option value="%s">%s</option>', $value, $label);
        }

        $html .= '</select></p>';
        return $html;
    }
}
```

### 2.2 JavaScript for Dynamic Field Management

```javascript
// assets/js/payop-checkout.js
class PayopCheckout {

    static renderDynamicFields(fields) {
        const container = document.getElementById('payop-dynamic-fields');
        container.innerHTML = '';
        container.style.display = 'block';

        fields.forEach(field => {
            const fieldElement = this.createFieldElement(field);
            container.appendChild(fieldElement);
        });

        // Initialize field dependencies and validation
        this.initializeFieldValidation();
        this.initializeFieldDependencies();
    }

    static createFieldElement(field) {
        const wrapper = document.createElement('p');
        wrapper.className = 'form-row';

        const label = document.createElement('label');
        label.setAttribute('for', `payop_${field.name}`);
        label.textContent = field.title || field.name.replace('_', ' ');
        if (field.required) label.textContent += ' *';

        let input;
        switch (field.type) {
            case 'email':
                input = this.createEmailField(field);
                break;
            case 'text':
                input = this.createTextField(field);
                break;
            case 'bank_code':
                input = this.createBankCodeField(field);
                break;
            case 'bank_type':
                input = this.createBankTypeField(field);
                break;
            case 'bank_country':
                input = this.createBankCountryField(field);
                break;
            default:
                input = this.createTextField(field);
        }

        wrapper.appendChild(label);
        wrapper.appendChild(input);

        return wrapper;
    }

    static createEmailField(field) {
        const input = document.createElement('input');
        input.type = 'email';
        input.id = `payop_${field.name}`;
        input.name = `payop_${field.name}`;
        input.className = 'input-text';
        input.required = field.required;
        return input;
    }

    static createTextField(field) {
        const input = document.createElement('input');
        input.type = 'text';
        input.id = `payop_${field.name}`;
        input.name = `payop_${field.name}`;
        input.className = 'input-text';
        input.required = field.required;

        if (field.regexp) {
            input.pattern = field.regexp;
        }

        return input;
    }

    static initializeFieldValidation() {
        // Add real-time validation for fields with regex patterns
        document.querySelectorAll('input[pattern]').forEach(input => {
            input.addEventListener('blur', function() {
                const pattern = new RegExp(this.pattern);
                if (this.value && !pattern.test(this.value)) {
                    this.setCustomValidity('Please enter a valid format');
                } else {
                    this.setCustomValidity('');
                }
            });
        });
    }

    static initializeFieldDependencies() {
        // Handle field dependencies (e.g., bank_country affects available bank_codes)
        const countryField = document.getElementById('payop_bank_country');
        const bankCodeField = document.getElementById('payop_bank_code');

        if (countryField && bankCodeField) {
            countryField.addEventListener('change', function() {
                PayopCheckout.updateBankCodes(this.value, bankCodeField);
            });
        }
    }

    static updateBankCodes(country, bankCodeField) {
        // Fetch bank codes for selected country
        fetch(`${payop_ajax.ajax_url}?action=payop_get_bank_codes&country=${country}`)
            .then(response => response.json())
            .then(data => {
                bankCodeField.innerHTML = '<option value="">Choose your bank...</option>';
                data.bank_codes.forEach(bank => {
                    const option = document.createElement('option');
                    option.value = bank.code;
                    option.textContent = bank.name;
                    bankCodeField.appendChild(option);
                });
            });
    }
}
```

### 2.3 WooCommerce Block Checkout Compatibility

#### Block Registration for Grouped Payment Methods
```php
// includes/blocks/class-payop-blocks-integration.php
class Payop_Blocks_Integration {

    public function initialize() {
        add_action('woocommerce_blocks_payment_method_type_registration', [$this, 'register_payment_methods']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_block_scripts']);
    }

    public function register_payment_methods($payment_method_registry) {
        $enabled_groups = get_option('payop_enabled_groups', []);
        $enabled_standalone = get_option('payop_enabled_standalone_methods', []);

        // Register grouped payment methods
        foreach ($enabled_groups as $group_id) {
            $payment_method_registry->register(
                new Payop_Group_Payment_Method_Type($group_id)
            );
        }

        // Register standalone payment methods
        foreach ($enabled_standalone as $method_id) {
            $payment_method_registry->register(
                new Payop_Individual_Payment_Method_Type($method_id)
            );
        }
    }

    public function enqueue_block_scripts() {
        if (is_admin() || !WC()->is_rest_api_request()) {
            return;
        }

        wp_enqueue_script(
            'payop-blocks',
            plugin_dir_url(__FILE__) . '../assets/js/payop-blocks.js',
            ['wc-blocks-registry', 'wc-settings', 'wp-element', 'wp-html-entities'],
            PAYOP_VERSION,
            true
        );

        wp_localize_script('payop-blocks', 'payopBlocksData', [
            'payment_methods' => $this->get_payment_methods_config(),
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_blocks_nonce')
        ]);
    }
}
```

#### React Components for Block Checkout
```javascript
// assets/js/payop-blocks.js
import { registerPaymentMethod } from '@woocommerce/blocks-registry';
import { createElement, useState, useEffect } from '@wordpress/element';
import { __ } from '@wordpress/i18n';

// Group Payment Method Component
const PayopGroupPaymentMethod = ({ group_id, group_config }) => {
    const [selectedMethod, setSelectedMethod] = useState('');
    const [dynamicFields, setDynamicFields] = useState([]);
    const [formData, setFormData] = useState({});

    const handleMethodSelection = (methodId) => {
        setSelectedMethod(methodId);
        const method = group_config.methods.find(m => m.identifier === methodId);
        setDynamicFields(method ? method.config.fields : []);
        setFormData({});
    };

    const handleFieldChange = (fieldName, value) => {
        setFormData(prev => ({
            ...prev,
            [fieldName]: value
        }));
    };

    const renderMethodSelection = () => {
        if (group_config.methods.length === 1) {
            // Auto-select single method
            useEffect(() => {
                handleMethodSelection(group_config.methods[0].identifier);
            }, []);
            return null;
        }

        return createElement('div', { className: 'payop-method-selection' }, [
            createElement('p', { key: 'title' }, __('Choose your payment method:', 'payop')),
            ...group_config.methods.map(method =>
                createElement('label', {
                    key: method.identifier,
                    className: 'payop-method-option'
                }, [
                    createElement('input', {
                        type: 'radio',
                        name: 'payop_method',
                        value: method.identifier,
                        checked: selectedMethod === method.identifier,
                        onChange: () => handleMethodSelection(method.identifier)
                    }),
                    createElement('div', { className: 'method-info' }, [
                        createElement('img', {
                            src: method.logo,
                            alt: method.title,
                            className: 'method-logo'
                        }),
                        createElement('span', { className: 'method-title' }, method.title)
                    ])
                ])
            )
        ]);
    };

    const renderDynamicFields = () => {
        if (!dynamicFields.length) return null;

        return createElement('div', { className: 'payop-dynamic-fields' },
            dynamicFields.map(field => renderField(field))
        );
    };

    const renderField = (field) => {
        const fieldId = `payop_${field.name}`;

        switch (field.type) {
            case 'email':
                return createElement('p', { key: field.name, className: 'form-row' }, [
                    createElement('label', { htmlFor: fieldId },
                        field.title || field.name + (field.required ? ' *' : '')
                    ),
                    createElement('input', {
                        type: 'email',
                        id: fieldId,
                        required: field.required,
                        value: formData[field.name] || '',
                        onChange: (e) => handleFieldChange(field.name, e.target.value),
                        className: 'input-text'
                    })
                ]);

            case 'text':
                return createElement('p', { key: field.name, className: 'form-row' }, [
                    createElement('label', { htmlFor: fieldId },
                        field.title || field.name + (field.required ? ' *' : '')
                    ),
                    createElement('input', {
                        type: 'text',
                        id: fieldId,
                        required: field.required,
                        pattern: field.regexp || undefined,
                        value: formData[field.name] || '',
                        onChange: (e) => handleFieldChange(field.name, e.target.value),
                        className: 'input-text'
                    })
                ]);

            case 'bank_code':
                return renderBankCodeField(field);

            case 'bank_type':
                return renderBankTypeField(field);

            default:
                return renderTextField(field);
        }
    };

    const renderBankCodeField = (field) => {
        const [bankCodes, setBankCodes] = useState([]);

        useEffect(() => {
            // Fetch bank codes based on selected country
            const country = formData.bank_country;
            if (country) {
                fetchBankCodes(country).then(setBankCodes);
            }
        }, [formData.bank_country]);

        return createElement('p', { key: field.name, className: 'form-row' }, [
            createElement('label', { htmlFor: `payop_${field.name}` }, 'Select Bank *'),
            createElement('select', {
                id: `payop_${field.name}`,
                required: field.required,
                value: formData[field.name] || '',
                onChange: (e) => handleFieldChange(field.name, e.target.value),
                className: 'select'
            }, [
                createElement('option', { value: '' }, 'Choose your bank...'),
                ...bankCodes.map(bank =>
                    createElement('option', { key: bank.code, value: bank.code }, bank.name)
                )
            ])
        ]);
    };

    return createElement('div', { className: 'payop-group-payment-method' }, [
        renderMethodSelection(),
        renderDynamicFields()
    ]);
};

// Register each group as a payment method
payopBlocksData.payment_methods.groups.forEach(group => {
    registerPaymentMethod({
        name: `payop_group_${group.id}`,
        label: group.title,
        content: createElement(PayopGroupPaymentMethod, {
            group_id: group.id,
            group_config: group
        }),
        edit: createElement(PayopGroupPaymentMethod, {
            group_id: group.id,
            group_config: group
        }),
        canMakePayment: () => true,
        ariaLabel: group.title,
        supports: {
            features: ['products']
        }
    });
});
```

## 3. Technical Architecture

### 3.1 Plugin Structure for Grouped Payment Methods

```
payop-woo/
├── payop-woo.php                          # Main plugin file
├── includes/
│   ├── class-payop-plugin.php             # Main plugin class
│   ├── class-payop-api-client.php         # PayOp API integration
│   ├── class-payop-payment-method-manager.php # Payment method management
│   ├── class-payop-group-manager.php      # Group management logic
│   ├── class-payop-webhook-handler.php    # IPN webhook handling
│   ├── class-payop-field-renderer.php     # Dynamic field generation
│   ├── gateways/
│   │   ├── class-payop-group-gateway.php  # Base group gateway
│   │   ├── class-payop-individual-gateway.php # Individual method gateway
│   │   ├── groups/
│   │   │   ├── class-payop-bank-transfer-group.php
│   │   │   ├── class-payop-cards-group.php
│   │   │   ├── class-payop-ewallet-group.php
│   │   │   ├── class-payop-cash-group.php
│   │   │   └── class-payop-crypto-group.php
│   │   └── individual/
│   │       ├── class-payop-method-{id}.php # Auto-generated classes
│   ├── blocks/
│   │   ├── class-payop-blocks-integration.php
│   │   ├── class-payop-group-payment-method-type.php
│   │   └── class-payop-individual-payment-method-type.php
│   └── admin/
│       ├── class-payop-admin.php
│       ├── class-payop-admin-payment-methods.php
│       └── views/
│           ├── admin-main.php
│           ├── admin-payment-methods.php
│           └── admin-groups.php
├── assets/
│   ├── css/
│   │   ├── admin.css
│   │   ├── checkout.css
│   │   └── blocks.css
│   ├── js/
│   │   ├── admin.js
│   │   ├── payop-checkout.js
│   │   └── payop-blocks.js
│   └── images/
│       ├── payment-icons/
│       └── group-icons/
├── templates/
│   ├── checkout/
│   │   ├── group-selection.php
│   │   ├── method-selection.php
│   │   └── payment-fields.php
│   └── admin/
└── languages/
```

### 3.2 Database Schema

#### Payment Method Groups Table
```sql
CREATE TABLE {$wpdb->prefix}payop_payment_groups (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    group_id varchar(50) NOT NULL,
    title varchar(255) NOT NULL,
    description text,
    icon varchar(255),
    grouping_type enum('type', 'country', 'currency', 'custom') DEFAULT 'type',
    enabled tinyint(1) DEFAULT 1,
    sort_order int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY group_id (group_id),
    KEY enabled (enabled),
    KEY sort_order (sort_order)
);
```

#### Payment Method Configurations Table
```sql
CREATE TABLE {$wpdb->prefix}payop_payment_methods (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    payop_method_id int(11) NOT NULL,
    title varchar(255) NOT NULL,
    type varchar(50) NOT NULL,
    logo varchar(255),
    currencies text, -- JSON array
    countries text,  -- JSON array
    config text,     -- JSON configuration from PayOp API
    group_id varchar(50),
    enabled tinyint(1) DEFAULT 1,
    standalone tinyint(1) DEFAULT 0,
    sort_order int(11) DEFAULT 0,
    last_synced datetime,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY payop_method_id (payop_method_id),
    KEY group_id (group_id),
    KEY enabled (enabled),
    KEY type (type),
    FOREIGN KEY (group_id) REFERENCES {$wpdb->prefix}payop_payment_groups(group_id) ON DELETE SET NULL
);
```

#### Group Method Relationships Table
```sql
CREATE TABLE {$wpdb->prefix}payop_group_methods (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    group_id varchar(50) NOT NULL,
    payop_method_id int(11) NOT NULL,
    sort_order int(11) DEFAULT 0,
    enabled tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY group_method (group_id, payop_method_id),
    KEY group_id (group_id),
    KEY payop_method_id (payop_method_id),
    KEY sort_order (sort_order),
    FOREIGN KEY (group_id) REFERENCES {$wpdb->prefix}payop_payment_groups(group_id) ON DELETE CASCADE,
    FOREIGN KEY (payop_method_id) REFERENCES {$wpdb->prefix}payop_payment_methods(payop_method_id) ON DELETE CASCADE
);
```

#### Order Payment Data Table (HPOS Compatible)
```sql
CREATE TABLE {$wpdb->prefix}payop_order_data (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    order_id bigint(20) unsigned NOT NULL,
    payop_invoice_id varchar(255),
    payop_transaction_id varchar(255),
    payop_method_id int(11),
    group_id varchar(50),
    payment_data text, -- JSON data collected from checkout
    status varchar(50) DEFAULT 'pending',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY order_id (order_id),
    KEY payop_invoice_id (payop_invoice_id),
    KEY payop_transaction_id (payop_transaction_id),
    KEY status (status)
);
```

### 3.3 API Integration Layer

#### PayOp API Client with Caching
```php
class Payop_API_Client {

    private $cache_duration = 3600; // 1 hour

    public function get_available_payment_methods($force_refresh = false) {
        $cache_key = 'payop_payment_methods_' . get_option('payop_application_id');

        if (!$force_refresh) {
            $cached_methods = get_transient($cache_key);
            if ($cached_methods !== false) {
                return $cached_methods;
            }
        }

        $response = $this->make_request('GET',
            '/v1/instrument-settings/payment-methods/available-for-application/' . get_option('payop_application_id'),
            [],
            ['Authorization' => 'Bearer ' . get_option('payop_jwt_token')]
        );

        if ($response && isset($response['data'])) {
            set_transient($cache_key, $response['data'], $this->cache_duration);
            $this->sync_payment_methods($response['data']);
            return $response['data'];
        }

        return [];
    }

    private function sync_payment_methods($methods) {
        global $wpdb;

        foreach ($methods as $method) {
            $wpdb->replace(
                $wpdb->prefix . 'payop_payment_methods',
                [
                    'payop_method_id' => $method['identifier'],
                    'title' => $method['title'],
                    'type' => $method['type'],
                    'logo' => $method['logo'],
                    'currencies' => json_encode($method['currencies']),
                    'countries' => json_encode($method['countries']),
                    'config' => json_encode($method['config']),
                    'last_synced' => current_time('mysql')
                ],
                ['%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s']
            );
        }
    }

    public function create_invoice($order_data, $payment_method_id = null) {
        $signature = $this->generate_signature(
            $order_data['amount'],
            $order_data['currency'],
            $order_data['order_id']
        );

        $invoice_data = [
            'publicKey' => get_option('payop_public_key'),
            'order' => [
                'id' => $order_data['order_id'],
                'amount' => $order_data['amount'],
                'currency' => $order_data['currency'],
                'description' => $order_data['description'],
                'items' => $order_data['items'] ?? []
            ],
            'signature' => $signature,
            'payer' => [
                'email' => $order_data['payer_email']
            ],
            'language' => get_locale(),
            'resultUrl' => $order_data['return_url'],
            'failPath' => $order_data['cancel_url']
        ];

        if ($payment_method_id) {
            $invoice_data['paymentMethod'] = $payment_method_id;
        }

        return $this->make_request('POST', '/v1/invoices/create', $invoice_data);
    }

    private function generate_signature($amount, $currency, $order_id) {
        $secret_key = get_option('payop_secret_key');
        $data = [$amount, $currency, $order_id, $secret_key];
        return hash('sha256', implode(':', $data));
    }
}
```

## 4. Integration Approach Decision

### 4.1 Recommended Approach: Invoice Preprocessing with Grouping

Based on our API analysis and the grouping requirements, the **Invoice Preprocessing Redirection** approach is optimal for the following reasons:

#### Why Invoice Preprocessing is Best for Grouped Methods:

1. **Simplified Group Flow**:
   - Group selection → Method selection → Field collection → Single redirection
   - No complex polling or multiple API calls required

2. **Consistent User Experience**:
   - All payment methods follow the same flow pattern
   - PayOp handles final gateway redirection and security

3. **Reduced Complexity**:
   - Single integration pattern for all 100+ payment methods
   - Grouping logic handled entirely on WooCommerce side

4. **Better Performance**:
   - No status polling required
   - Immediate redirection after invoice creation

#### Implementation Flow for Grouped Methods:

```php
class WC_Payop_Group_Gateway extends WC_Payment_Gateway {

    public function process_payment($order_id) {
        $order = wc_get_order($order_id);

        // 1. Get selected method and collected data
        $selected_method = $_POST['payop_selected_method'];
        $payment_data = $this->collect_payment_data($_POST);

        // 2. Validate payment data against method requirements
        $validation_result = $this->validate_payment_data($selected_method, $payment_data);
        if (!$validation_result['valid']) {
            wc_add_notice($validation_result['message'], 'error');
            return ['result' => 'failure'];
        }

        // 3. Create PayOp invoice with specific method
        $invoice_data = $this->prepare_invoice_data($order, $selected_method, $payment_data);
        $invoice_response = $this->api_client->create_invoice($invoice_data, $selected_method);

        if (!$invoice_response || !isset($invoice_response['data'])) {
            wc_add_notice(__('Payment initialization failed. Please try again.', 'payop'), 'error');
            return ['result' => 'failure'];
        }

        // 4. Store payment data for webhook processing
        $this->store_order_payment_data($order_id, [
            'payop_invoice_id' => $invoice_response['data'],
            'payop_method_id' => $selected_method,
            'group_id' => $this->id,
            'payment_data' => $payment_data
        ]);

        // 5. Redirect to PayOp preprocessing (bypasses method selection)
        $redirect_url = sprintf(
            'https://checkout.payop.com/%s/payment/invoice-preprocessing/%s',
            $this->get_language_code(),
            $invoice_response['data']
        );

        return [
            'result' => 'success',
            'redirect' => $redirect_url
        ];
    }

    private function collect_payment_data($post_data) {
        $payment_data = [];

        foreach ($post_data as $key => $value) {
            if (strpos($key, 'payop_') === 0) {
                $field_name = str_replace('payop_', '', $key);
                $payment_data[$field_name] = sanitize_text_field($value);
            }
        }

        return $payment_data;
    }

    private function validate_payment_data($method_id, $payment_data) {
        $method_config = $this->get_method_config($method_id);

        foreach ($method_config['fields'] as $field) {
            if ($field['required'] && empty($payment_data[$field['name']])) {
                return [
                    'valid' => false,
                    'message' => sprintf(__('%s is required.', 'payop'), $field['title'] ?? $field['name'])
                ];
            }

            if (!empty($payment_data[$field['name']]) && isset($field['regexp'])) {
                if (!preg_match('/' . $field['regexp'] . '/', $payment_data[$field['name']])) {
                    return [
                        'valid' => false,
                        'message' => sprintf(__('Invalid format for %s.', 'payop'), $field['title'] ?? $field['name'])
                    ];
                }
            }
        }

        return ['valid' => true];
    }
}
```

### 4.2 How Grouping Affects Integration

#### Group-Specific Considerations:

1. **Bank Transfer Groups**:
   - Country-specific bank code validation
   - SEPA vs non-SEPA handling
   - Real-time bank code fetching

2. **Card Payment Groups**:
   - Minimal field requirements (email, name)
   - Faster checkout process
   - Universal country support

3. **E-wallet Groups**:
   - Country-specific validation (e.g., CPF for Brazil)
   - Phone number requirements
   - Regional wallet availability

4. **Cash Payment Groups**:
   - Location-based availability
   - Document number validation
   - Country-specific requirements

5. **Crypto Groups**:
   - Universal availability
   - Minimal field requirements
   - Currency conversion handling

#### Group Processing Logic:
```php
class Payop_Group_Manager {

    public function process_group_payment($group_id, $method_id, $payment_data, $order) {
        $group_config = $this->get_group_config($group_id);

        // Apply group-specific processing
        switch ($group_config['type']) {
            case 'bank_transfer':
                return $this->process_bank_transfer($method_id, $payment_data, $order);

            case 'cards_international':
                return $this->process_card_payment($method_id, $payment_data, $order);

            case 'ewallet':
                return $this->process_ewallet_payment($method_id, $payment_data, $order);

            case 'cash':
                return $this->process_cash_payment($method_id, $payment_data, $order);

            case 'crypto':
                return $this->process_crypto_payment($method_id, $payment_data, $order);

            default:
                return $this->process_generic_payment($method_id, $payment_data, $order);
        }
    }

    private function process_bank_transfer($method_id, $payment_data, $order) {
        // Validate bank-specific requirements
        if (empty($payment_data['bank_code']) || empty($payment_data['bank_type'])) {
            throw new Exception(__('Bank details are required for bank transfers.', 'payop'));
        }

        // Add bank transfer specific metadata
        $payment_data['transfer_type'] = 'bank_transfer';
        $payment_data['processing_time'] = '1-3 business days';

        return $this->create_invoice_with_method($method_id, $payment_data, $order);
    }

    private function process_card_payment($method_id, $payment_data, $order) {
        // Card payments have minimal requirements
        $payment_data['payment_type'] = 'card';
        $payment_data['processing_time'] = 'instant';

        return $this->create_invoice_with_method($method_id, $payment_data, $order);
    }
}
```

## 5. Implementation Phases

### Phase 1: Foundation & Core Infrastructure (Weeks 1-2)

#### Week 1: Plugin Foundation
- [ ] **Plugin Structure Setup**
  - Create plugin directory structure
  - Set up autoloading and namespace organization
  - Implement plugin activation/deactivation hooks
  - Create database tables for payment methods and groups

- [ ] **PayOp API Integration**
  - Implement `Payop_API_Client` class with signature generation
  - Add payment methods synchronization from PayOp API
  - Implement caching mechanism for API responses
  - Create webhook handler for IPN processing

- [ ] **Admin Settings Framework**
  - Basic admin menu and settings pages
  - API credentials configuration (public key, secret key, JWT)
  - Test connection functionality
  - Environment switching (sandbox/live)

#### Week 2: Payment Method Management
- [ ] **Payment Method Sync & Storage**
  - Automatic sync of 100+ payment methods from PayOp
  - Database storage with method configurations
  - Payment method enable/disable functionality
  - Method categorization by type, country, currency

- [ ] **Group Management System**
  - Create payment method groups (type, country, currency)
  - Group assignment interface
  - Standalone method configuration
  - Group enable/disable controls

### Phase 2: Grouping System & Admin Interface (Weeks 3-4)

#### Week 3: Admin Interface Development
- [ ] **Payment Method Management UI**
  - Payment methods listing with search/filter
  - Bulk enable/disable operations
  - Method details view with field requirements
  - Group assignment drag-and-drop interface

- [ ] **Grouping Configuration**
  - Group creation and editing interface
  - Group strategy selection (type/country/currency)
  - Group ordering and priority settings
  - Preview of checkout appearance

#### Week 4: Group Logic Implementation
- [ ] **Group Processing Logic**
  - `Payop_Group_Manager` implementation
  - Group-specific validation rules
  - Method selection within groups
  - Fallback handling for ungrouped methods

- [ ] **Dynamic Field System**
  - `Payop_Field_Renderer` class
  - Field type handlers (email, text, bank_code, etc.)
  - Field validation and sanitization
  - Conditional field display logic

### Phase 3: Checkout Integration (Weeks 5-6)

#### Week 5: Classic Checkout Implementation
- [ ] **Group Gateway Classes**
  - Base `WC_Payop_Group_Gateway` class
  - Individual group gateway implementations
  - Payment processing flow
  - Method selection and field collection

- [ ] **Dynamic Checkout Forms**
  - JavaScript for dynamic field rendering
  - Method selection handling
  - Real-time field validation
  - Form submission processing

#### Week 6: Block Checkout Integration
- [ ] **Block Components Development**
  - React components for grouped payment methods
  - Payment method selection interface
  - Dynamic field rendering in blocks
  - Block registration and configuration

- [ ] **Block-Specific Features**
  - Block editor integration
  - Block settings and customization
  - Block validation and error handling
  - Block checkout flow testing

### Phase 4: Payment Processing & Integration (Weeks 7-8)

#### Week 7: Payment Flow Implementation
- [ ] **Invoice Creation & Processing**
  - Group-specific invoice creation
  - Payment data collection and validation
  - PayOp API integration for invoice creation
  - Redirection to PayOp preprocessing

- [ ] **Order Management**
  - HPOS-compatible order data storage
  - Payment status tracking
  - Order notes and metadata
  - Failed payment handling

#### Week 8: Webhook & Status Management
- [ ] **IPN Webhook Processing**
  - Webhook endpoint implementation
  - Payment status updates
  - Order completion handling
  - Security validation (IP whitelisting)

- [ ] **Status Synchronization**
  - Payment status polling (backup)
  - Order status updates
  - Customer notifications
  - Admin notifications

### Phase 5: Testing & Quality Assurance (Weeks 9-10)

#### Week 9: Comprehensive Testing
- [ ] **Payment Method Testing**
  - Test each payment method group
  - Field validation testing
  - Error handling verification
  - Cross-browser compatibility

- [ ] **Integration Testing**
  - WooCommerce compatibility testing
  - HPOS functionality verification
  - Block checkout testing
  - Theme compatibility testing

#### Week 10: Performance & Security
- [ ] **Performance Optimization**
  - API response caching optimization
  - Database query optimization
  - Frontend performance testing
  - Load testing with multiple payment methods

- [ ] **Security Audit**
  - Input validation and sanitization
  - SQL injection prevention
  - XSS protection
  - API security verification

### Phase 6: Documentation & Deployment (Weeks 11-12)

#### Week 11: Documentation
- [ ] **User Documentation**
  - Admin setup guide
  - Payment method configuration guide
  - Troubleshooting documentation
  - FAQ and common issues

- [ ] **Developer Documentation**
  - Code documentation and comments
  - API integration guide
  - Customization hooks and filters
  - Extension development guide

#### Week 12: Final Testing & Release
- [ ] **Final Testing Round**
  - End-to-end testing with real transactions
  - Multi-currency testing
  - Multi-language testing
  - Accessibility testing

- [ ] **Release Preparation**
  - Version tagging and release notes
  - WordPress.org submission preparation
  - Support documentation
  - Launch strategy planning

## 6. Testing Strategies for Grouped Payment Methods

### 6.1 Group-Specific Testing

#### Bank Transfer Group Testing
```bash
# Test bank transfer group with different countries
curl -X POST "https://api.payop.com/v1/invoices/create" \
 -H "Content-Type: application/json" \
 -d '{
   "publicKey": "application-606",
   "order": {"id": "test-bank-de", "amount": "10.00", "currency": "EUR", "items": []},
   "signature": "SIGNATURE",
   "payer": {"email": "<EMAIL>"},
   "paymentMethod": 200017
 }'

# Test with different bank types (SEPA, SEPA_INSTANT)
# Test with various bank codes
# Test field validation (date_of_birth format, bank_country restrictions)
```

#### E-wallet Group Testing
```bash
# Test PIX (Brazil)
curl -X POST "https://api.payop.com/v1/invoices/create" \
 -H "Content-Type: application/json" \
 -d '{
   "publicKey": "application-606",
   "order": {"id": "test-pix", "amount": "10.00", "currency": "EUR", "items": []},
   "signature": "SIGNATURE",
   "payer": {"email": "<EMAIL>"},
   "paymentMethod": 200969
 }'

# Test CPF/CNPJ validation
# Test country restrictions
# Test currency limitations
```

### 6.2 Automated Testing Framework

#### PHPUnit Tests for Group Logic
```php
class Test_Payop_Group_Manager extends WP_UnitTestCase {

    public function test_bank_transfer_group_validation() {
        $group_manager = new Payop_Group_Manager();

        // Test valid bank transfer data
        $valid_data = [
            'email' => '<EMAIL>',
            'name' => 'John Doe',
            'date_of_birth' => '01.01.1990',
            'bank_code' => 'DEUTDEFF',
            'bank_type' => 'SEPA',
            'bank_country' => 'DE'
        ];

        $result = $group_manager->validate_payment_data(200017, $valid_data);
        $this->assertTrue($result['valid']);

        // Test invalid data
        $invalid_data = $valid_data;
        unset($invalid_data['bank_code']);

        $result = $group_manager->validate_payment_data(200017, $invalid_data);
        $this->assertFalse($result['valid']);
    }

    public function test_group_method_assignment() {
        $group_manager = new Payop_Group_Manager();

        // Test automatic grouping by type
        $methods = $group_manager->get_methods_by_type('bank_transfer');
        $this->assertContains(200017, array_column($methods, 'identifier'));

        // Test country-based grouping
        $eu_methods = $group_manager->get_methods_by_country(['DE', 'FR', 'ES']);
        $this->assertNotEmpty($eu_methods);
    }
}
```

#### JavaScript Testing for Dynamic Fields
```javascript
// Jest tests for dynamic field rendering
describe('PayopCheckout', () => {
    test('renders bank transfer fields correctly', () => {
        const fields = [
            {name: 'email', type: 'email', required: true},
            {name: 'bank_code', type: 'bank_code', required: true},
            {name: 'bank_type', type: 'bank_type', required: true}
        ];

        PayopCheckout.renderDynamicFields(fields);

        expect(document.getElementById('payop_email')).toBeTruthy();
        expect(document.getElementById('payop_bank_code')).toBeTruthy();
        expect(document.getElementById('payop_bank_type')).toBeTruthy();
    });

    test('validates required fields', () => {
        const field = {name: 'email', type: 'email', required: true};
        const input = PayopCheckout.createEmailField(field);

        expect(input.required).toBe(true);
        expect(input.type).toBe('email');
    });
});
```

### 6.3 HPOS Compatibility Testing

```php
class Test_Payop_HPOS_Compatibility extends WP_UnitTestCase {

    public function test_order_data_storage_hpos() {
        // Enable HPOS
        update_option('woocommerce_custom_orders_table_enabled', 'yes');

        $order = wc_create_order();
        $order_id = $order->get_id();

        // Test PayOp data storage
        $payop_data = [
            'payop_invoice_id' => 'test-invoice-123',
            'payop_method_id' => 200017,
            'group_id' => 'bank_transfer'
        ];

        $payop_order_manager = new Payop_Order_Manager();
        $payop_order_manager->store_payment_data($order_id, $payop_data);

        // Verify data retrieval
        $stored_data = $payop_order_manager->get_payment_data($order_id);
        $this->assertEquals($payop_data['payop_invoice_id'], $stored_data['payop_invoice_id']);
    }
}
```

---

**Comprehensive Development Plan Complete**: This plan provides a detailed roadmap for implementing a grouped payment method system for PayOp integration with WooCommerce, addressing all specified requirements including dynamic checkout, block compatibility, HPOS support, and comprehensive testing strategies.
```
