# PayOp API Analysis for WooCommerce Plugin Development

## Overview
This document provides a comprehensive analysis of the PayOp API for developing a WooCommerce payment plugin that bypasses PayOp's hosted checkout page and provides individual payment methods as separate options.

## Test Credentials
- **Public Key**: application-606
- **Project ID**: 606
- **Secret Key**: fd6d7b9d6e14146ba064cd3b7afd7a0e
- **JWT Token**: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTUzNCwidGltZSI6MTc1MDUxOTkyOSwidHdvRmFjdG9yIjp7InBhc3NlZCI6dHJ1ZX0sInRva2VuSWQiOjYyNDMsImV4cGlyZWRBdCI6MTc1MTMxNzE5OSwicm9sZSI6MSwiYWNjZXNzVG9rZW4iOiJiNzIyMzY4Mjc0M2U3YzYzNjYzZDU1MTUifQ.CfbiRB9f7OoTSpAzjOfpx_L9dUmkDGBAT1ZixzQe-fc

## API Integration Types

PayOp offers two integration methods:

### 1. Hosted Page Integration (Simple)
- User redirected to PayOp's checkout page
- PayOp handles all payment method selection and processing
- Minimal development effort required

### 2. Direct Integration (Advanced) - **Our Target**
- Merchant collects payment method-specific data
- Direct redirection to payment gateway
- Full control over checkout experience
- Ideal for our WooCommerce plugin requirements

## Core API Endpoints Analysis

### 1. Get Available Payment Methods
**Endpoint**: `GET /v1/instrument-settings/payment-methods/available-for-application/{APPLICATION_ID}`
**Authentication**: Bearer JWT Token required
**Purpose**: Retrieve all available payment methods with their required fields

### 2. Create Invoice
**Endpoint**: `POST /v1/invoices/create`
**Authentication**: Public key + signature
**Purpose**: Create payment invoice before processing

### 3. Create Checkout Transaction
**Endpoint**: `POST /v1/checkout/create`
**Authentication**: Bearer JWT Token required
**Purpose**: Process payment with collected customer data

### 4. Check Invoice Status
**Endpoint**: `GET /v1/checkout/check-invoice-status/{invoiceID}`
**Purpose**: Poll for payment status updates

## API Testing Results

### Test 1: Get Available Payment Methods
**Status**: ✅ SUCCESS
**Endpoint**: `GET /v1/instrument-settings/payment-methods/available-for-application/606`
**Authentication**: Bearer JWT Token

**Key Findings**:
- **Total Payment Methods**: 100+ individual payment methods available
- **Payment Types**:
  - `bank_transfer` - Bank transfers and online banking
  - `cash` - Cash payment locations
  - `ewallet` - Digital wallets
  - `cards_international` - Credit/debit cards
  - `crypto` - Cryptocurrency payments

**Sample Payment Methods Analyzed**:

1. **Cards via PayDo** (ID: 700001)
   - Type: `cards_international`
   - Currencies: EUR, AUD, CAD, GBP, USD, DKK
   - Countries: 100+ countries supported
   - Required Fields: email, name

2. **Pay by bank** (ID: 200018)
   - Type: `bank_transfer`
   - Currencies: EUR
   - Countries: AT, ES, IT, PT, FR, DE, FI, NL, EE, LT, IE, PL
   - Required Fields: email, name, date_of_birth, bank_code, bank_type, bank_country

3. **Crypto Payment** (ID: 6110)
   - Type: `crypto`
   - Currencies: EUR
   - Countries: Global support (100+ countries)
   - Required Fields: email, name

4. **PIX** (ID: 200969)
   - Type: `ewallet`
   - Currencies: EUR
   - Countries: BR
   - Required Fields: email, name, cpf_cnpj

**Field Types Discovered**:
- `email` - Email validation
- `text` - General text input
- `bank_code` - Bank identifier codes
- `bank_type` - Transfer type (SEPA, SEPA_INSTANT, FPS)
- `bank_country` - Country codes
- `iban_type` - IBAN classification
- Custom fields with regex validation (e.g., CPF/CNPJ, document numbers)

**Critical Insight**: Each payment method has its own identifier, supported currencies, countries, and required field configuration. This confirms that individual payment methods can be implemented as separate WooCommerce payment gateways.
