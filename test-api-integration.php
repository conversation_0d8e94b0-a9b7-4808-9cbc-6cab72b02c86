<?php
/**
 * Test PayOp API Integration
 * This script tests the PayOp API client with the test credentials
 */

// Test credentials from analysis
$test_credentials = [
    'public_key' => 'application-606',
    'secret_key' => 'fd6d7b9d6e14146ba064cd3b7afd7a0e',
    'jwt_token' => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6MTUzNCwidGltZSI6MTc1MDUxOTkyOSwidHdvRmFjdG9yIjp7InBhc3NlZCI6dHJ1ZX0sInRva2VuSWQiOjYyNDMsImV4cGlyZWRBdCI6MTc1MTMxNzE5OSwicm9sZSI6MSwiYWNjZXNzVG9rZW4iOiJiNzIyMzY4Mjc0M2U3YzYzNjYzZDU1MTUifQ.CfbiRB9f7OoTSpAzjOfpx_L9dUmkDGBAT1ZixzQe-fc',
    'application_id' => '606'
];

echo "=== PayOp API Integration Test ===\n\n";

// Test 1: Get Available Payment Methods
echo "Test 1: Getting available payment methods...\n";

$url = "https://api.payop.com/v1/instrument-settings/payment-methods/available-for-application/{$test_credentials['application_id']}";
$headers = [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $test_credentials['jwt_token']
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code === 200) {
    $data = json_decode($response, true);
    if (isset($data['data']) && is_array($data['data'])) {
        echo "✅ SUCCESS: Retrieved " . count($data['data']) . " payment methods\n";
        
        // Show sample methods by type
        $types = [];
        foreach ($data['data'] as $method) {
            $type = $method['type'];
            if (!isset($types[$type])) {
                $types[$type] = [];
            }
            $types[$type][] = $method['title'];
        }
        
        echo "\nPayment methods by type:\n";
        foreach ($types as $type => $methods) {
            echo "- {$type}: " . count($methods) . " methods\n";
            echo "  Examples: " . implode(', ', array_slice($methods, 0, 3)) . "\n";
        }
    } else {
        echo "❌ ERROR: Invalid response format\n";
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
} else {
    echo "❌ ERROR: HTTP {$http_code}\n";
    echo "Response: " . substr($response, 0, 200) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Create Invoice
echo "Test 2: Creating test invoice...\n";

$amount = "10.00";
$currency = "EUR";
$order_id = "test-" . time();

// Generate signature
$signature_data = [$amount, $currency, $order_id, $test_credentials['secret_key']];
$signature = hash('sha256', implode(':', $signature_data));

$invoice_data = [
    'publicKey' => $test_credentials['public_key'],
    'order' => [
        'id' => $order_id,
        'amount' => $amount,
        'currency' => $currency,
        'description' => 'Test Invoice for Plugin Development',
        'items' => []
    ],
    'signature' => $signature,
    'payer' => [
        'email' => '<EMAIL>'
    ],
    'language' => 'en',
    'resultUrl' => 'https://example.com/success',
    'failPath' => 'https://example.com/fail'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.payop.com/v1/invoices/create');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoice_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code === 200) {
    $data = json_decode($response, true);
    if (isset($data['data']) && isset($data['status']) && $data['status'] == 1) {
        $invoice_id = $data['data'];
        echo "✅ SUCCESS: Invoice created with ID: {$invoice_id}\n";
        echo "Signature used: {$signature}\n";
        echo "Order ID: {$order_id}\n";
        
        // Test invoice preprocessing URL
        $preprocessing_url = "https://checkout.payop.com/en/payment/invoice-preprocessing/{$invoice_id}";
        echo "Preprocessing URL: {$preprocessing_url}\n";
        
        // Test URL accessibility
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $preprocessing_url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            echo "✅ Preprocessing URL is accessible\n";
        } else {
            echo "⚠️  Preprocessing URL returned HTTP {$http_code}\n";
        }
        
    } else {
        echo "❌ ERROR: Invalid invoice response\n";
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
} else {
    echo "❌ ERROR: HTTP {$http_code}\n";
    echo "Response: " . substr($response, 0, 200) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Create Invoice with Specific Payment Method
echo "Test 3: Creating invoice with specific payment method...\n";

$order_id_2 = "test-method-" . time();
$signature_2 = hash('sha256', implode(':', [$amount, $currency, $order_id_2, $test_credentials['secret_key']]));

$invoice_data_2 = [
    'publicKey' => $test_credentials['public_key'],
    'order' => [
        'id' => $order_id_2,
        'amount' => $amount,
        'currency' => $currency,
        'description' => 'Test Invoice with Payment Method',
        'items' => []
    ],
    'signature' => $signature_2,
    'payer' => [
        'email' => '<EMAIL>'
    ],
    'paymentMethod' => 200018, // Pay by bank method from our analysis
    'language' => 'en',
    'resultUrl' => 'https://example.com/success',
    'failPath' => 'https://example.com/fail'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.payop.com/v1/invoices/create');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($invoice_data_2));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code === 200) {
    $data = json_decode($response, true);
    if (isset($data['data']) && isset($data['status']) && $data['status'] == 1) {
        $invoice_id_2 = $data['data'];
        echo "✅ SUCCESS: Invoice with payment method created: {$invoice_id_2}\n";
        
        // Get invoice details to see payment method configuration
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.payop.com/v1/invoices/{$invoice_id_2}");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            $invoice_details = json_decode($response, true);
            if (isset($invoice_details['data']['paymentMethod'])) {
                $payment_method = $invoice_details['data']['paymentMethod'];
                echo "✅ Payment method configuration retrieved:\n";
                echo "  Method ID: {$payment_method['identifier']}\n";
                echo "  Fields required: " . count($payment_method['fields']) . "\n";
                
                foreach ($payment_method['fields'] as $field) {
                    echo "    - {$field['name']} ({$field['type']}) " . ($field['required'] ? '[required]' : '[optional]') . "\n";
                }
            }
        }
        
    } else {
        echo "❌ ERROR: Invalid invoice response\n";
        echo "Response: " . substr($response, 0, 200) . "...\n";
    }
} else {
    echo "❌ ERROR: HTTP {$http_code}\n";
    echo "Response: " . substr($response, 0, 200) . "...\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "API Integration Test Complete!\n";
echo str_repeat("=", 50) . "\n";
?>
