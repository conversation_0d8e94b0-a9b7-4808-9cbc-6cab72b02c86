<?php

namespace Payop\Admin;

/**
 * Admin Interface
 */
class Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
        add_action('wp_ajax_payop_test_connection', [$this, 'ajax_test_connection']);
        add_action('wp_ajax_payop_sync_methods', [$this, 'ajax_sync_methods']);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('PayOp Settings', 'payop-woo'),
            __('PayOp', 'payop-woo'),
            'manage_woocommerce',
            'payop-settings',
            [$this, 'settings_page'],
            'dashicons-money-alt',
            56
        );
        
        add_submenu_page(
            'payop-settings',
            __('PayOp Settings', 'payop-woo'),
            __('Settings', 'payop-woo'),
            'manage_woocommerce',
            'payop-settings',
            [$this, 'settings_page']
        );
        
        add_submenu_page(
            'payop-settings',
            __('Payment Methods', 'payop-woo'),
            __('Payment Methods', 'payop-woo'),
            'manage_woocommerce',
            'payop-payment-methods',
            [$this, 'payment_methods_page']
        );
        
        add_submenu_page(
            'payop-settings',
            __('Groups', 'payop-woo'),
            __('Groups', 'payop-woo'),
            'manage_woocommerce',
            'payop-groups',
            [$this, 'groups_page']
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // API Settings
        register_setting('payop_api_settings', 'payop_environment');
        register_setting('payop_api_settings', 'payop_public_key');
        register_setting('payop_api_settings', 'payop_secret_key');
        register_setting('payop_api_settings', 'payop_jwt_token');
        register_setting('payop_api_settings', 'payop_application_id');
        
        // General Settings
        register_setting('payop_general_settings', 'payop_grouping_strategy');
        register_setting('payop_general_settings', 'payop_enabled_groups');
        register_setting('payop_general_settings', 'payop_enabled_standalone_methods');
        register_setting('payop_general_settings', 'payop_cache_duration');
        register_setting('payop_general_settings', 'payop_webhook_enabled');
        register_setting('payop_general_settings', 'payop_remove_data_on_uninstall');
        
        // Add settings sections
        add_settings_section(
            'payop_api_section',
            __('API Configuration', 'payop-woo'),
            [$this, 'api_section_callback'],
            'payop_api_settings'
        );
        
        add_settings_section(
            'payop_general_section',
            __('General Settings', 'payop-woo'),
            [$this, 'general_section_callback'],
            'payop_general_settings'
        );
        
        // Add settings fields
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // API Fields
        add_settings_field(
            'payop_environment',
            __('Environment', 'payop-woo'),
            [$this, 'environment_field'],
            'payop_api_settings',
            'payop_api_section'
        );
        
        add_settings_field(
            'payop_public_key',
            __('Public Key', 'payop-woo'),
            [$this, 'public_key_field'],
            'payop_api_settings',
            'payop_api_section'
        );
        
        add_settings_field(
            'payop_secret_key',
            __('Secret Key', 'payop-woo'),
            [$this, 'secret_key_field'],
            'payop_api_settings',
            'payop_api_section'
        );
        
        add_settings_field(
            'payop_jwt_token',
            __('JWT Token', 'payop-woo'),
            [$this, 'jwt_token_field'],
            'payop_api_settings',
            'payop_api_section'
        );
        
        add_settings_field(
            'payop_application_id',
            __('Application ID', 'payop-woo'),
            [$this, 'application_id_field'],
            'payop_api_settings',
            'payop_api_section'
        );
        
        // General Fields
        add_settings_field(
            'payop_grouping_strategy',
            __('Grouping Strategy', 'payop-woo'),
            [$this, 'grouping_strategy_field'],
            'payop_general_settings',
            'payop_general_section'
        );
        
        add_settings_field(
            'payop_cache_duration',
            __('Cache Duration (seconds)', 'payop-woo'),
            [$this, 'cache_duration_field'],
            'payop_general_settings',
            'payop_general_section'
        );
        
        add_settings_field(
            'payop_webhook_enabled',
            __('Enable Webhooks', 'payop-woo'),
            [$this, 'webhook_enabled_field'],
            'payop_general_settings',
            'payop_general_section'
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'payop') === false) {
            return;
        }
        
        wp_enqueue_script(
            'payop-admin',
            PAYOP_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery'],
            PAYOP_VERSION,
            true
        );
        
        wp_enqueue_style(
            'payop-admin',
            PAYOP_PLUGIN_URL . 'assets/css/admin.css',
            [],
            PAYOP_VERSION
        );
        
        wp_localize_script('payop-admin', 'payopAdmin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_admin_nonce'),
            'strings' => [
                'testing_connection' => __('Testing connection...', 'payop-woo'),
                'connection_success' => __('Connection successful!', 'payop-woo'),
                'connection_failed' => __('Connection failed!', 'payop-woo'),
                'syncing_methods' => __('Syncing payment methods...', 'payop-woo'),
                'sync_success' => __('Payment methods synced successfully!', 'payop-woo'),
                'sync_failed' => __('Failed to sync payment methods!', 'payop-woo')
            ]
        ]);
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="payop-admin-container">
                <div class="payop-admin-main">
                    <form method="post" action="options.php">
                        <?php
                        settings_fields('payop_api_settings');
                        do_settings_sections('payop_api_settings');
                        ?>
                        
                        <div class="payop-test-connection">
                            <button type="button" id="payop-test-connection" class="button button-secondary">
                                <?php _e('Test Connection', 'payop-woo'); ?>
                            </button>
                            <span id="payop-connection-result"></span>
                        </div>
                        
                        <hr>
                        
                        <?php
                        settings_fields('payop_general_settings');
                        do_settings_sections('payop_general_settings');
                        submit_button();
                        ?>
                    </form>
                </div>
                
                <div class="payop-admin-sidebar">
                    <div class="payop-info-box">
                        <h3><?php _e('PayOp Information', 'payop-woo'); ?></h3>
                        <p><?php _e('PayOp is a payment aggregator that provides access to 100+ payment methods worldwide.', 'payop-woo'); ?></p>
                        
                        <h4><?php _e('Webhook URL', 'payop-woo'); ?></h4>
                        <code><?php echo esc_url(\Payop\Webhook\Handler::get_webhook_url()); ?></code>
                        
                        <h4><?php _e('Last Sync', 'payop-woo'); ?></h4>
                        <p><?php echo get_option('payop_last_sync', __('Never', 'payop-woo')); ?></p>
                        
                        <button type="button" id="payop-sync-methods" class="button button-primary">
                            <?php _e('Sync Payment Methods', 'payop-woo'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Payment methods page
     */
    public function payment_methods_page() {
        $payment_methods = new PaymentMethods();
        $payment_methods->render_page();
    }
    
    /**
     * Groups page
     */
    public function groups_page() {
        $groups = new Groups();
        $groups->render_page();
    }
    
    /**
     * API section callback
     */
    public function api_section_callback() {
        echo '<p>' . __('Configure your PayOp API credentials below.', 'payop-woo') . '</p>';
    }
    
    /**
     * General section callback
     */
    public function general_section_callback() {
        echo '<p>' . __('Configure general plugin settings.', 'payop-woo') . '</p>';
    }
    
    /**
     * Environment field
     */
    public function environment_field() {
        $value = get_option('payop_environment', 'sandbox');
        ?>
        <select name="payop_environment">
            <option value="sandbox" <?php selected($value, 'sandbox'); ?>><?php _e('Sandbox', 'payop-woo'); ?></option>
            <option value="live" <?php selected($value, 'live'); ?>><?php _e('Live', 'payop-woo'); ?></option>
        </select>
        <p class="description"><?php _e('Select the environment for PayOp API.', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * Public key field
     */
    public function public_key_field() {
        $value = get_option('payop_public_key', '');
        ?>
        <input type="text" name="payop_public_key" value="<?php echo esc_attr($value); ?>" class="regular-text" />
        <p class="description"><?php _e('Your PayOp public key (e.g., application-606).', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * Secret key field
     */
    public function secret_key_field() {
        $value = get_option('payop_secret_key', '');
        ?>
        <input type="password" name="payop_secret_key" value="<?php echo esc_attr($value); ?>" class="regular-text" />
        <p class="description"><?php _e('Your PayOp secret key for signature generation.', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * JWT token field
     */
    public function jwt_token_field() {
        $value = get_option('payop_jwt_token', '');
        ?>
        <textarea name="payop_jwt_token" rows="3" class="large-text"><?php echo esc_textarea($value); ?></textarea>
        <p class="description"><?php _e('Your PayOp JWT token for API authentication.', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * Application ID field
     */
    public function application_id_field() {
        $value = get_option('payop_application_id', '');
        ?>
        <input type="text" name="payop_application_id" value="<?php echo esc_attr($value); ?>" class="regular-text" />
        <p class="description"><?php _e('Your PayOp application ID (e.g., 606).', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * Grouping strategy field
     */
    public function grouping_strategy_field() {
        $value = get_option('payop_grouping_strategy', 'type');
        ?>
        <select name="payop_grouping_strategy">
            <option value="type" <?php selected($value, 'type'); ?>><?php _e('Group by Payment Type', 'payop-woo'); ?></option>
            <option value="country" <?php selected($value, 'country'); ?>><?php _e('Group by Country', 'payop-woo'); ?></option>
            <option value="currency" <?php selected($value, 'currency'); ?>><?php _e('Group by Currency', 'payop-woo'); ?></option>
            <option value="standalone" <?php selected($value, 'standalone'); ?>><?php _e('Show All Methods Individually', 'payop-woo'); ?></option>
        </select>
        <p class="description"><?php _e('Choose how to organize payment methods on checkout.', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * Cache duration field
     */
    public function cache_duration_field() {
        $value = get_option('payop_cache_duration', 3600);
        ?>
        <input type="number" name="payop_cache_duration" value="<?php echo esc_attr($value); ?>" min="300" max="86400" />
        <p class="description"><?php _e('How long to cache payment methods data (300-86400 seconds).', 'payop-woo'); ?></p>
        <?php
    }
    
    /**
     * Webhook enabled field
     */
    public function webhook_enabled_field() {
        $value = get_option('payop_webhook_enabled', true);
        ?>
        <label>
            <input type="checkbox" name="payop_webhook_enabled" value="1" <?php checked($value); ?> />
            <?php _e('Enable webhook processing for payment status updates', 'payop-woo'); ?>
        </label>
        <?php
    }
    
    /**
     * AJAX test connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Insufficient permissions', 'payop-woo'));
        }
        
        $api_client = \Payop\API\Client::instance();
        $result = $api_client->test_connection();
        
        wp_send_json($result);
    }
    
    /**
     * AJAX sync methods
     */
    public function ajax_sync_methods() {
        check_ajax_referer('payop_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Insufficient permissions', 'payop-woo'));
        }
        
        $method_manager = \Payop\Payment\MethodManager::instance();
        $result = $method_manager->sync_payment_methods();
        
        if ($result) {
            wp_send_json_success([
                'message' => __('Payment methods synced successfully!', 'payop-woo')
            ]);
        } else {
            wp_send_json_error([
                'message' => __('Failed to sync payment methods!', 'payop-woo')
            ]);
        }
    }
}
