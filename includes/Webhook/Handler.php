<?php

namespace Payop\Webhook;

/**
 * PayOp Webhook Handler
 */
class Handler {
    
    /**
     * Allowed IP addresses for PayOp webhooks
     */
    private const ALLOWED_IPS = [
        '*************',
        '*************',
        '************',
        '*************'
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', [$this, 'add_webhook_endpoint']);
        add_action('wp_loaded', [$this, 'handle_webhook']);
    }
    
    /**
     * Add webhook endpoint
     */
    public function add_webhook_endpoint() {
        add_rewrite_rule(
            '^payop-webhook/?$',
            'index.php?payop_webhook=1',
            'top'
        );
        
        add_filter('query_vars', function($vars) {
            $vars[] = 'payop_webhook';
            return $vars;
        });
    }
    
    /**
     * Handle webhook request
     */
    public function handle_webhook() {
        if (!get_query_var('payop_webhook')) {
            return;
        }
        
        // Verify request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->send_response(405, 'Method not allowed');
            return;
        }
        
        // Verify IP address
        if (!$this->verify_ip_address()) {
            $this->log_webhook_error('Invalid IP address: ' . $this->get_client_ip());
            $this->send_response(403, 'Forbidden');
            return;
        }
        
        // Get webhook payload
        $payload = $this->get_webhook_payload();
        if (!$payload) {
            $this->log_webhook_error('Invalid payload');
            $this->send_response(400, 'Bad request');
            return;
        }
        
        // Process webhook
        $result = $this->process_webhook($payload);
        
        if ($result) {
            $this->send_response(200, 'OK');
        } else {
            $this->send_response(500, 'Internal server error');
        }
    }
    
    /**
     * Verify IP address
     */
    private function verify_ip_address() {
        $client_ip = $this->get_client_ip();
        return in_array($client_ip, self::ALLOWED_IPS);
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_headers = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ip_headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }
    
    /**
     * Get webhook payload
     */
    private function get_webhook_payload() {
        $raw_payload = file_get_contents('php://input');
        
        if (empty($raw_payload)) {
            return null;
        }
        
        $payload = json_decode($raw_payload, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return null;
        }
        
        return $payload;
    }
    
    /**
     * Process webhook
     */
    private function process_webhook($payload) {
        try {
            // Log webhook for debugging
            $this->log_webhook('Received webhook', $payload);
            
            // Validate payload structure
            if (!$this->validate_payload($payload)) {
                $this->log_webhook_error('Invalid payload structure', $payload);
                return false;
            }
            
            // Extract data
            $invoice_id = $payload['invoice']['id'] ?? null;
            $invoice_status = $payload['invoice']['status'] ?? null;
            $transaction_id = $payload['transaction']['id'] ?? null;
            $transaction_state = $payload['transaction']['state'] ?? null;
            $order_id = $payload['transaction']['order']['id'] ?? null;
            
            if (!$order_id) {
                $this->log_webhook_error('Missing order ID in webhook', $payload);
                return false;
            }
            
            // Get WooCommerce order
            $order = wc_get_order($order_id);
            if (!$order) {
                $this->log_webhook_error("Order not found: {$order_id}", $payload);
                return false;
            }
            
            // Update order based on transaction state
            $this->update_order_status($order, $transaction_state, $payload);
            
            // Update PayOp order data
            $this->update_payop_order_data($order_id, [
                'payop_invoice_id' => $invoice_id,
                'payop_transaction_id' => $transaction_id,
                'status' => $this->get_status_from_state($transaction_state)
            ]);
            
            return true;
            
        } catch (Exception $e) {
            $this->log_webhook_error('Webhook processing error: ' . $e->getMessage(), $payload);
            return false;
        }
    }
    
    /**
     * Validate payload structure
     */
    private function validate_payload($payload) {
        $required_fields = [
            'invoice.id',
            'invoice.status',
            'transaction.id',
            'transaction.state',
            'transaction.order.id'
        ];
        
        foreach ($required_fields as $field) {
            $keys = explode('.', $field);
            $value = $payload;
            
            foreach ($keys as $key) {
                if (!isset($value[$key])) {
                    return false;
                }
                $value = $value[$key];
            }
        }
        
        return true;
    }
    
    /**
     * Update order status based on transaction state
     */
    private function update_order_status($order, $transaction_state, $payload) {
        $order_id = $order->get_id();
        
        switch ($transaction_state) {
            case 1: // Pending
                $order->update_status('pending', __('Payment pending via PayOp.', 'payop-woo'));
                break;
                
            case 2: // Success
                $order->payment_complete();
                $order->add_order_note(__('Payment completed via PayOp.', 'payop-woo'));
                break;
                
            case 3: // Failed
                $order->update_status('failed', __('Payment failed via PayOp.', 'payop-woo'));
                break;
                
            case 4: // Cancelled
                $order->update_status('cancelled', __('Payment cancelled via PayOp.', 'payop-woo'));
                break;
                
            case 5: // Refunded
                $order->update_status('refunded', __('Payment refunded via PayOp.', 'payop-woo'));
                break;
                
            default:
                $order->add_order_note(sprintf(__('Unknown payment state %d received from PayOp.', 'payop-woo'), $transaction_state));
        }
        
        // Add transaction ID to order
        if (!empty($payload['transaction']['id'])) {
            $order->set_transaction_id($payload['transaction']['id']);
            $order->save();
        }
    }
    
    /**
     * Update PayOp order data
     */
    private function update_payop_order_data($order_id, $data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_order_data';
        
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE order_id = %d",
            $order_id
        ));
        
        if ($existing) {
            $wpdb->update(
                $table_name,
                $data,
                ['order_id' => $order_id],
                ['%s', '%s', '%s'],
                ['%d']
            );
        } else {
            $data['order_id'] = $order_id;
            $wpdb->insert($table_name, $data);
        }
    }
    
    /**
     * Get status from transaction state
     */
    private function get_status_from_state($state) {
        $status_map = [
            1 => 'pending',
            2 => 'completed',
            3 => 'failed',
            4 => 'cancelled',
            5 => 'refunded'
        ];
        
        return $status_map[$state] ?? 'unknown';
    }
    
    /**
     * Send HTTP response
     */
    private function send_response($code, $message) {
        status_header($code);
        echo $message;
        exit;
    }
    
    /**
     * Log webhook
     */
    private function log_webhook($message, $data = null) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $log_message = "PayOp Webhook: {$message}";
            if ($data) {
                $log_message .= ' - Data: ' . json_encode($data);
            }
            error_log($log_message);
        }
    }
    
    /**
     * Log webhook error
     */
    private function log_webhook_error($message, $data = null) {
        $log_message = "PayOp Webhook Error: {$message}";
        if ($data) {
            $log_message .= ' - Data: ' . json_encode($data);
        }
        error_log($log_message);
    }
    
    /**
     * Get webhook URL
     */
    public static function get_webhook_url() {
        return home_url('/payop-webhook/');
    }
}
