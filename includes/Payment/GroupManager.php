<?php

namespace Payop\Payment;

/**
 * Payment Group Manager
 */
class GroupManager {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize hooks if needed
    }
    
    /**
     * Get all payment groups
     */
    public function get_all_groups() {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        $groups = $wpdb->get_results(
            "SELECT * FROM $groups_table ORDER BY sort_order, title"
        );
        
        $result = [];
        foreach ($groups as $group) {
            $result[] = [
                'id' => $group->group_id,
                'title' => $group->title,
                'description' => $group->description,
                'icon' => $group->icon,
                'grouping_type' => $group->grouping_type,
                'enabled' => (bool) $group->enabled,
                'sort_order' => $group->sort_order,
                'methods_count' => $this->get_group_methods_count($group->group_id)
            ];
        }
        
        return $result;
    }
    
    /**
     * Get enabled groups
     */
    public function get_enabled_groups() {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        $groups = $wpdb->get_results(
            "SELECT * FROM $groups_table WHERE enabled = 1 ORDER BY sort_order"
        );
        
        $result = [];
        foreach ($groups as $group) {
            $result[] = [
                'id' => $group->group_id,
                'title' => $group->title,
                'description' => $group->description,
                'icon' => $group->icon,
                'grouping_type' => $group->grouping_type,
                'sort_order' => $group->sort_order,
                'methods' => $this->get_group_methods($group->group_id)
            ];
        }
        
        return $result;
    }
    
    /**
     * Get group configuration
     */
    public function get_group_config($group_id) {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        $group = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $groups_table WHERE group_id = %s",
            $group_id
        ));
        
        if (!$group) {
            return null;
        }
        
        return [
            'id' => $group->group_id,
            'title' => $group->title,
            'description' => $group->description,
            'icon' => $group->icon,
            'grouping_type' => $group->grouping_type,
            'enabled' => (bool) $group->enabled,
            'sort_order' => $group->sort_order,
            'methods' => $this->get_group_methods($group_id)
        ];
    }
    
    /**
     * Get methods in a group
     */
    public function get_group_methods($group_id) {
        $method_manager = MethodManager::instance();
        return $method_manager->get_methods_by_group($group_id);
    }
    
    /**
     * Get group methods count
     */
    public function get_group_methods_count($group_id) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        $group_methods_table = $wpdb->prefix . 'payop_group_methods';
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT m.payop_method_id) 
             FROM $methods_table m
             LEFT JOIN $group_methods_table gm ON m.payop_method_id = gm.payop_method_id
             WHERE (m.group_id = %s OR gm.group_id = %s) 
             AND m.enabled = 1 
             AND (gm.enabled IS NULL OR gm.enabled = 1)",
            $group_id,
            $group_id
        ));
        
        return (int) $count;
    }
    
    /**
     * Enable group
     */
    public function enable_group($group_id) {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        return $wpdb->update(
            $groups_table,
            ['enabled' => 1],
            ['group_id' => $group_id],
            ['%d'],
            ['%s']
        );
    }
    
    /**
     * Disable group
     */
    public function disable_group($group_id) {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        return $wpdb->update(
            $groups_table,
            ['enabled' => 0],
            ['group_id' => $group_id],
            ['%d'],
            ['%s']
        );
    }
    
    /**
     * Add method to group
     */
    public function add_method_to_group($group_id, $method_id, $sort_order = 0) {
        global $wpdb;
        
        $group_methods_table = $wpdb->prefix . 'payop_group_methods';
        
        // Check if relationship already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $group_methods_table WHERE group_id = %s AND payop_method_id = %d",
            $group_id,
            $method_id
        ));
        
        if ($existing) {
            return $wpdb->update(
                $group_methods_table,
                ['sort_order' => $sort_order, 'enabled' => 1],
                ['group_id' => $group_id, 'payop_method_id' => $method_id],
                ['%d', '%d'],
                ['%s', '%d']
            );
        } else {
            return $wpdb->insert(
                $group_methods_table,
                [
                    'group_id' => $group_id,
                    'payop_method_id' => $method_id,
                    'sort_order' => $sort_order,
                    'enabled' => 1
                ],
                ['%s', '%d', '%d', '%d']
            );
        }
    }
    
    /**
     * Remove method from group
     */
    public function remove_method_from_group($group_id, $method_id) {
        global $wpdb;
        
        $group_methods_table = $wpdb->prefix . 'payop_group_methods';
        
        return $wpdb->delete(
            $group_methods_table,
            ['group_id' => $group_id, 'payop_method_id' => $method_id],
            ['%s', '%d']
        );
    }
    
    /**
     * Update group
     */
    public function update_group($group_id, $data) {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        $allowed_fields = ['title', 'description', 'icon', 'enabled', 'sort_order'];
        $update_data = [];
        $format = [];
        
        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $update_data[$field] = $data[$field];
                $format[] = is_numeric($data[$field]) ? '%d' : '%s';
            }
        }
        
        if (empty($update_data)) {
            return false;
        }
        
        return $wpdb->update(
            $groups_table,
            $update_data,
            ['group_id' => $group_id],
            $format,
            ['%s']
        );
    }
    
    /**
     * Create custom group
     */
    public function create_group($group_id, $title, $description = '', $icon = '', $grouping_type = 'custom') {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        
        // Check if group already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $groups_table WHERE group_id = %s",
            $group_id
        ));
        
        if ($existing) {
            return false;
        }
        
        // Get next sort order
        $max_sort = $wpdb->get_var("SELECT MAX(sort_order) FROM $groups_table");
        $sort_order = ($max_sort ?? 0) + 1;
        
        return $wpdb->insert(
            $groups_table,
            [
                'group_id' => $group_id,
                'title' => $title,
                'description' => $description,
                'icon' => $icon,
                'grouping_type' => $grouping_type,
                'enabled' => 1,
                'sort_order' => $sort_order
            ],
            ['%s', '%s', '%s', '%s', '%s', '%d', '%d']
        );
    }
    
    /**
     * Delete group
     */
    public function delete_group($group_id) {
        global $wpdb;
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        $group_methods_table = $wpdb->prefix . 'payop_group_methods';
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        // Don't allow deletion of default groups
        $default_groups = ['bank_transfer', 'cards_international', 'ewallet', 'cash', 'crypto'];
        if (in_array($group_id, $default_groups)) {
            return false;
        }
        
        // Remove group method relationships
        $wpdb->delete($group_methods_table, ['group_id' => $group_id], ['%s']);
        
        // Update methods to remove group assignment
        $wpdb->update(
            $methods_table,
            ['group_id' => null],
            ['group_id' => $group_id],
            ['%s'],
            ['%s']
        );
        
        // Delete group
        return $wpdb->delete($groups_table, ['group_id' => $group_id], ['%s']);
    }
    
    /**
     * Get methods by country
     */
    public function get_methods_by_country($countries) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results(
            "SELECT * FROM $methods_table WHERE enabled = 1"
        );
        
        $result = [];
        foreach ($methods as $method) {
            $method_countries = json_decode($method->countries, true);
            if (is_array($method_countries) && array_intersect($countries, $method_countries)) {
                $result[] = [
                    'identifier' => $method->payop_method_id,
                    'title' => $method->title,
                    'type' => $method->type,
                    'logo' => $method->logo,
                    'currencies' => json_decode($method->currencies, true),
                    'countries' => $method_countries,
                    'config' => json_decode($method->config, true)
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * Get methods by currency
     */
    public function get_methods_by_currency($currency) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results(
            "SELECT * FROM $methods_table WHERE enabled = 1"
        );
        
        $result = [];
        foreach ($methods as $method) {
            $method_currencies = json_decode($method->currencies, true);
            if (is_array($method_currencies) && in_array($currency, $method_currencies)) {
                $result[] = [
                    'identifier' => $method->payop_method_id,
                    'title' => $method->title,
                    'type' => $method->type,
                    'logo' => $method->logo,
                    'currencies' => $method_currencies,
                    'countries' => json_decode($method->countries, true),
                    'config' => json_decode($method->config, true)
                ];
            }
        }
        
        return $result;
    }
}
