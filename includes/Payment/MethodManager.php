<?php

namespace Payop\Payment;

/**
 * Payment Method Manager
 */
class MethodManager {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('payop_sync_payment_methods', [$this, 'sync_payment_methods']);
    }
    
    /**
     * Get enabled payment gateways for WooCommerce
     */
    public function get_enabled_gateways() {
        $gateways = [];
        $grouping_strategy = get_option('payop_grouping_strategy', 'type');
        
        if ($grouping_strategy === 'standalone') {
            // Individual payment methods
            $gateways = $this->get_individual_gateways();
        } else {
            // Grouped payment methods
            $gateways = $this->get_group_gateways();
            
            // Add standalone methods
            $standalone_gateways = $this->get_standalone_gateways();
            $gateways = array_merge($gateways, $standalone_gateways);
        }
        
        return $gateways;
    }
    
    /**
     * Get group gateways
     */
    private function get_group_gateways() {
        global $wpdb;
        
        $gateways = [];
        $enabled_groups = get_option('payop_enabled_groups', []);
        
        if (empty($enabled_groups)) {
            return $gateways;
        }
        
        $groups_table = $wpdb->prefix . 'payop_payment_groups';
        $placeholders = implode(',', array_fill(0, count($enabled_groups), '%s'));
        
        $groups = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $groups_table WHERE group_id IN ($placeholders) AND enabled = 1 ORDER BY sort_order",
            ...$enabled_groups
        ));
        
        foreach ($groups as $group) {
            $gateway_class = $this->get_group_gateway_class($group->group_id);
            if (class_exists($gateway_class)) {
                $gateways[] = $gateway_class;
            }
        }
        
        return $gateways;
    }
    
    /**
     * Get individual gateways
     */
    private function get_individual_gateways() {
        global $wpdb;
        
        $gateways = [];
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results(
            "SELECT * FROM $methods_table WHERE enabled = 1 ORDER BY sort_order"
        );
        
        foreach ($methods as $method) {
            $gateway_class = $this->get_individual_gateway_class($method->payop_method_id);
            if (class_exists($gateway_class)) {
                $gateways[] = $gateway_class;
            }
        }
        
        return $gateways;
    }
    
    /**
     * Get standalone gateways
     */
    private function get_standalone_gateways() {
        global $wpdb;
        
        $gateways = [];
        $enabled_standalone = get_option('payop_enabled_standalone_methods', []);
        
        if (empty($enabled_standalone)) {
            return $gateways;
        }
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        $placeholders = implode(',', array_fill(0, count($enabled_standalone), '%d'));
        
        $methods = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $methods_table WHERE payop_method_id IN ($placeholders) AND enabled = 1 AND standalone = 1",
            ...$enabled_standalone
        ));
        
        foreach ($methods as $method) {
            $gateway_class = $this->get_individual_gateway_class($method->payop_method_id);
            if (class_exists($gateway_class)) {
                $gateways[] = $gateway_class;
            }
        }
        
        return $gateways;
    }
    
    /**
     * Get group gateway class name
     */
    private function get_group_gateway_class($group_id) {
        $class_map = [
            'bank_transfer' => '\\Payop\\Gateways\\Groups\\BankTransferGroup',
            'cards_international' => '\\Payop\\Gateways\\Groups\\CardsGroup',
            'ewallet' => '\\Payop\\Gateways\\Groups\\EwalletGroup',
            'cash' => '\\Payop\\Gateways\\Groups\\CashGroup',
            'crypto' => '\\Payop\\Gateways\\Groups\\CryptoGroup'
        ];
        
        return $class_map[$group_id] ?? '\\Payop\\Gateways\\GroupGateway';
    }
    
    /**
     * Get individual gateway class name
     */
    private function get_individual_gateway_class($method_id) {
        return "\\Payop\\Gateways\\Individual\\Method{$method_id}";
    }
    
    /**
     * Get payment method configuration
     */
    public function get_method_config($method_id) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $method = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $methods_table WHERE payop_method_id = %d",
            $method_id
        ));
        
        if (!$method) {
            return null;
        }
        
        return [
            'id' => $method->payop_method_id,
            'title' => $method->title,
            'type' => $method->type,
            'logo' => $method->logo,
            'currencies' => json_decode($method->currencies, true),
            'countries' => json_decode($method->countries, true),
            'config' => json_decode($method->config, true),
            'group_id' => $method->group_id
        ];
    }
    
    /**
     * Get methods by group
     */
    public function get_methods_by_group($group_id) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        $group_methods_table = $wpdb->prefix . 'payop_group_methods';
        
        $methods = $wpdb->get_results($wpdb->prepare(
            "SELECT m.*, gm.sort_order as group_sort_order 
             FROM $methods_table m
             LEFT JOIN $group_methods_table gm ON m.payop_method_id = gm.payop_method_id
             WHERE (m.group_id = %s OR gm.group_id = %s) 
             AND m.enabled = 1 
             AND (gm.enabled IS NULL OR gm.enabled = 1)
             ORDER BY COALESCE(gm.sort_order, m.sort_order)",
            $group_id,
            $group_id
        ));
        
        $result = [];
        foreach ($methods as $method) {
            $result[] = [
                'identifier' => $method->payop_method_id,
                'title' => $method->title,
                'type' => $method->type,
                'logo' => $method->logo,
                'currencies' => json_decode($method->currencies, true),
                'countries' => json_decode($method->countries, true),
                'config' => json_decode($method->config, true)
            ];
        }
        
        return $result;
    }
    
    /**
     * Get methods by type
     */
    public function get_methods_by_type($type) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $methods_table WHERE type = %s AND enabled = 1 ORDER BY sort_order",
            $type
        ));
        
        $result = [];
        foreach ($methods as $method) {
            $result[] = [
                'identifier' => $method->payop_method_id,
                'title' => $method->title,
                'type' => $method->type,
                'logo' => $method->logo,
                'currencies' => json_decode($method->currencies, true),
                'countries' => json_decode($method->countries, true),
                'config' => json_decode($method->config, true)
            ];
        }
        
        return $result;
    }
    
    /**
     * Sync payment methods from API
     */
    public function sync_payment_methods() {
        $api_client = \Payop\API\Client::instance();
        $methods = $api_client->get_available_payment_methods(true);
        
        if ($methods) {
            do_action('payop_payment_methods_synced', $methods);
            return true;
        }
        
        return false;
    }
    
    /**
     * Enable payment method
     */
    public function enable_method($method_id) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        return $wpdb->update(
            $methods_table,
            ['enabled' => 1],
            ['payop_method_id' => $method_id],
            ['%d'],
            ['%d']
        );
    }
    
    /**
     * Disable payment method
     */
    public function disable_method($method_id) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        return $wpdb->update(
            $methods_table,
            ['enabled' => 0],
            ['payop_method_id' => $method_id],
            ['%d'],
            ['%d']
        );
    }
    
    /**
     * Set method as standalone
     */
    public function set_standalone($method_id, $standalone = true) {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        return $wpdb->update(
            $methods_table,
            ['standalone' => $standalone ? 1 : 0],
            ['payop_method_id' => $method_id],
            ['%d'],
            ['%d']
        );
    }
    
    /**
     * Get all payment methods
     */
    public function get_all_methods() {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        
        $methods = $wpdb->get_results(
            "SELECT * FROM $methods_table ORDER BY type, sort_order, title"
        );
        
        $result = [];
        foreach ($methods as $method) {
            $result[] = [
                'identifier' => $method->payop_method_id,
                'title' => $method->title,
                'type' => $method->type,
                'logo' => $method->logo,
                'currencies' => json_decode($method->currencies, true),
                'countries' => json_decode($method->countries, true),
                'config' => json_decode($method->config, true),
                'group_id' => $method->group_id,
                'enabled' => (bool) $method->enabled,
                'standalone' => (bool) $method->standalone,
                'last_synced' => $method->last_synced
            ];
        }
        
        return $result;
    }
}
