<?php

namespace Payop\Blocks;

/**
 * WooCommerce Blocks Integration
 */
class Integration {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_block_scripts']);
    }
    
    /**
     * Register payment methods for blocks
     */
    public function register_payment_methods($payment_method_registry) {
        $grouping_strategy = get_option('payop_grouping_strategy', 'type');
        
        if ($grouping_strategy === 'standalone') {
            $this->register_individual_methods($payment_method_registry);
        } else {
            $this->register_group_methods($payment_method_registry);
            $this->register_standalone_methods($payment_method_registry);
        }
    }
    
    /**
     * Register group payment methods
     */
    private function register_group_methods($payment_method_registry) {
        $group_manager = \Payop\Payment\GroupManager::instance();
        $enabled_groups = get_option('payop_enabled_groups', []);
        
        foreach ($enabled_groups as $group_id) {
            $group_config = $group_manager->get_group_config($group_id);
            
            if ($group_config && $group_config['enabled']) {
                $payment_method_registry->register(
                    new GroupPaymentMethodType($group_id, $group_config)
                );
            }
        }
    }
    
    /**
     * Register individual payment methods
     */
    private function register_individual_methods($payment_method_registry) {
        $method_manager = \Payop\Payment\MethodManager::instance();
        $methods = $method_manager->get_all_methods();
        
        foreach ($methods as $method) {
            if ($method['enabled']) {
                $payment_method_registry->register(
                    new IndividualPaymentMethodType($method['identifier'], $method)
                );
            }
        }
    }
    
    /**
     * Register standalone payment methods
     */
    private function register_standalone_methods($payment_method_registry) {
        $enabled_standalone = get_option('payop_enabled_standalone_methods', []);
        $method_manager = \Payop\Payment\MethodManager::instance();
        
        foreach ($enabled_standalone as $method_id) {
            $method_config = $method_manager->get_method_config($method_id);
            
            if ($method_config) {
                $payment_method_registry->register(
                    new IndividualPaymentMethodType($method_id, $method_config)
                );
            }
        }
    }
    
    /**
     * Enqueue block scripts
     */
    public function enqueue_block_scripts() {
        if (is_admin() || !has_block('woocommerce/checkout')) {
            return;
        }
        
        wp_enqueue_script(
            'payop-blocks',
            PAYOP_PLUGIN_URL . 'assets/js/payop-blocks.js',
            ['wc-blocks-registry', 'wc-settings', 'wp-element', 'wp-html-entities'],
            PAYOP_VERSION,
            true
        );
        
        wp_localize_script('payop-blocks', 'payopBlocksData', [
            'payment_methods' => $this->get_payment_methods_config(),
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_blocks_nonce')
        ]);
    }
    
    /**
     * Get payment methods configuration for blocks
     */
    private function get_payment_methods_config() {
        $grouping_strategy = get_option('payop_grouping_strategy', 'type');
        $config = [
            'grouping_strategy' => $grouping_strategy,
            'groups' => [],
            'standalone' => []
        ];
        
        if ($grouping_strategy !== 'standalone') {
            $group_manager = \Payop\Payment\GroupManager::instance();
            $enabled_groups = get_option('payop_enabled_groups', []);
            
            foreach ($enabled_groups as $group_id) {
                $group_config = $group_manager->get_group_config($group_id);
                if ($group_config && $group_config['enabled']) {
                    $config['groups'][] = $group_config;
                }
            }
            
            // Add standalone methods
            $enabled_standalone = get_option('payop_enabled_standalone_methods', []);
            $method_manager = \Payop\Payment\MethodManager::instance();
            
            foreach ($enabled_standalone as $method_id) {
                $method_config = $method_manager->get_method_config($method_id);
                if ($method_config) {
                    $config['standalone'][] = $method_config;
                }
            }
        } else {
            // All methods as standalone
            $method_manager = \Payop\Payment\MethodManager::instance();
            $methods = $method_manager->get_all_methods();
            
            foreach ($methods as $method) {
                if ($method['enabled']) {
                    $config['standalone'][] = $method;
                }
            }
        }
        
        return $config;
    }
}

/**
 * Group Payment Method Type for Blocks
 */
class GroupPaymentMethodType extends \Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType {
    
    private $group_id;
    private $group_config;
    
    public function __construct($group_id, $group_config) {
        $this->group_id = $group_id;
        $this->group_config = $group_config;
        $this->name = 'payop_group_' . $group_id;
    }
    
    public function initialize() {
        $this->settings = get_option('payop_group_' . $this->group_id . '_settings', []);
    }
    
    public function is_active() {
        return !empty($this->group_config['enabled']);
    }
    
    public function get_payment_method_script_handles() {
        return ['payop-blocks'];
    }
    
    public function get_payment_method_data() {
        return [
            'title' => $this->group_config['title'],
            'description' => $this->group_config['description'],
            'group_id' => $this->group_id,
            'methods' => $this->group_config['methods'],
            'supports' => ['products']
        ];
    }
}

/**
 * Individual Payment Method Type for Blocks
 */
class IndividualPaymentMethodType extends \Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType {
    
    private $method_id;
    private $method_config;
    
    public function __construct($method_id, $method_config) {
        $this->method_id = $method_id;
        $this->method_config = $method_config;
        $this->name = 'payop_method_' . $method_id;
    }
    
    public function initialize() {
        $this->settings = get_option('payop_method_' . $this->method_id . '_settings', []);
    }
    
    public function is_active() {
        return !empty($this->method_config['enabled']);
    }
    
    public function get_payment_method_script_handles() {
        return ['payop-blocks'];
    }
    
    public function get_payment_method_data() {
        return [
            'title' => $this->method_config['title'],
            'description' => $this->method_config['title'],
            'method_id' => $this->method_id,
            'config' => $this->method_config['config'],
            'supports' => ['products']
        ];
    }
}
