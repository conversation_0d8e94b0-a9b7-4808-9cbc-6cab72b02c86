<?php

namespace Payop\API;

/**
 * PayOp API Client
 */
class Client {
    
    /**
     * API Base URL
     */
    private const API_BASE_URL = 'https://api.payop.com';
    
    /**
     * Cache duration (1 hour)
     */
    private const CACHE_DURATION = 3600;
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * API credentials
     */
    private $public_key;
    private $secret_key;
    private $jwt_token;
    private $application_id;
    
    /**
     * Get instance
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->load_credentials();
    }
    
    /**
     * Load API credentials
     */
    private function load_credentials() {
        $this->public_key = get_option('payop_public_key', '');
        $this->secret_key = get_option('payop_secret_key', '');
        $this->jwt_token = get_option('payop_jwt_token', '');
        $this->application_id = get_option('payop_application_id', '');
    }
    
    /**
     * Test API connection
     */
    public function test_connection() {
        if (empty($this->jwt_token) || empty($this->application_id)) {
            return [
                'success' => false,
                'message' => __('JWT token and Application ID are required.', 'payop-woo')
            ];
        }
        
        $response = $this->get_available_payment_methods(true);
        
        if ($response === false) {
            return [
                'success' => false,
                'message' => __('Failed to connect to PayOp API. Please check your credentials.', 'payop-woo')
            ];
        }
        
        return [
            'success' => true,
            'message' => sprintf(__('Successfully connected to PayOp API. Found %d payment methods.', 'payop-woo'), count($response)),
            'methods_count' => count($response)
        ];
    }
    
    /**
     * Get available payment methods
     */
    public function get_available_payment_methods($force_refresh = false) {
        $cache_key = 'payop_payment_methods_' . $this->application_id;
        
        if (!$force_refresh) {
            $cached_methods = get_transient($cache_key);
            if ($cached_methods !== false) {
                return $cached_methods;
            }
        }
        
        $endpoint = "/v1/instrument-settings/payment-methods/available-for-application/{$this->application_id}";
        $response = $this->make_request('GET', $endpoint, [], [
            'Authorization' => 'Bearer ' . $this->jwt_token
        ]);
        
        if ($response && isset($response['data'])) {
            set_transient($cache_key, $response['data'], self::CACHE_DURATION);
            $this->sync_payment_methods($response['data']);
            return $response['data'];
        }
        
        return false;
    }
    
    /**
     * Create invoice
     */
    public function create_invoice($order_data, $payment_method_id = null) {
        $signature = $this->generate_signature(
            $order_data['amount'],
            $order_data['currency'],
            $order_data['order_id']
        );
        
        $invoice_data = [
            'publicKey' => $this->public_key,
            'order' => [
                'id' => $order_data['order_id'],
                'amount' => $order_data['amount'],
                'currency' => $order_data['currency'],
                'description' => $order_data['description'],
                'items' => $order_data['items'] ?? []
            ],
            'signature' => $signature,
            'payer' => [
                'email' => $order_data['payer_email']
            ],
            'language' => $this->get_language_code(),
            'resultUrl' => $order_data['return_url'],
            'failPath' => $order_data['cancel_url']
        ];
        
        if ($payment_method_id) {
            $invoice_data['paymentMethod'] = $payment_method_id;
        }
        
        return $this->make_request('POST', '/v1/invoices/create', $invoice_data);
    }
    
    /**
     * Get invoice details
     */
    public function get_invoice($invoice_id) {
        return $this->make_request('GET', "/v1/invoices/{$invoice_id}");
    }
    
    /**
     * Check invoice status
     */
    public function check_invoice_status($invoice_id) {
        return $this->make_request('GET', "/v1/checkout/check-invoice-status/{$invoice_id}");
    }
    
    /**
     * Generate signature
     */
    public function generate_signature($amount, $currency, $order_id) {
        $data = [$amount, $currency, $order_id, $this->secret_key];
        return hash('sha256', implode(':', $data));
    }
    
    /**
     * Make API request
     */
    private function make_request($method, $endpoint, $data = [], $headers = []) {
        $url = self::API_BASE_URL . $endpoint;
        
        $default_headers = [
            'Content-Type' => 'application/json',
            'User-Agent' => 'PayOp-WooCommerce/' . PAYOP_VERSION
        ];
        
        $headers = array_merge($default_headers, $headers);
        
        $args = [
            'method' => $method,
            'headers' => $headers,
            'timeout' => 30,
            'sslverify' => true
        ];
        
        if (!empty($data) && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $args['body'] = json_encode($data);
        }
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            error_log('PayOp API Error: ' . $response->get_error_message());
            return false;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 400) {
            error_log("PayOp API Error: HTTP {$status_code} - {$body}");
            return false;
        }
        
        $decoded = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log('PayOp API Error: Invalid JSON response');
            return false;
        }
        
        return $decoded;
    }
    
    /**
     * Sync payment methods to database
     */
    private function sync_payment_methods($methods) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_payment_methods';
        
        foreach ($methods as $method) {
            $data = [
                'payop_method_id' => $method['identifier'],
                'title' => $method['title'],
                'type' => $method['type'],
                'logo' => $method['logo'] ?? '',
                'currencies' => json_encode($method['currencies'] ?? []),
                'countries' => json_encode($method['countries'] ?? []),
                'config' => json_encode($method['config'] ?? []),
                'last_synced' => current_time('mysql')
            ];
            
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE payop_method_id = %d",
                $method['identifier']
            ));
            
            if ($existing) {
                $wpdb->update($table_name, $data, ['payop_method_id' => $method['identifier']]);
            } else {
                // Auto-assign to group based on type
                $data['group_id'] = $this->get_default_group_for_type($method['type']);
                $wpdb->insert($table_name, $data);
            }
        }
        
        // Update last sync time
        update_option('payop_last_sync', current_time('mysql'));
    }
    
    /**
     * Get default group for payment type
     */
    private function get_default_group_for_type($type) {
        $type_mapping = [
            'bank_transfer' => 'bank_transfer',
            'cards_international' => 'cards_international',
            'ewallet' => 'ewallet',
            'cash' => 'cash',
            'crypto' => 'crypto'
        ];
        
        return $type_mapping[$type] ?? null;
    }
    
    /**
     * Get language code for PayOp
     */
    private function get_language_code() {
        $locale = get_locale();
        $language = substr($locale, 0, 2);
        
        // PayOp supported languages
        $supported = ['en', 'ru', 'de', 'fr', 'es', 'it', 'pt'];
        
        return in_array($language, $supported) ? $language : 'en';
    }
    
    /**
     * Clear cache
     */
    public function clear_cache() {
        delete_transient('payop_payment_methods_' . $this->application_id);
    }
    
    /**
     * Set credentials
     */
    public function set_credentials($public_key, $secret_key, $jwt_token, $application_id) {
        $this->public_key = $public_key;
        $this->secret_key = $secret_key;
        $this->jwt_token = $jwt_token;
        $this->application_id = $application_id;
        
        // Clear cache when credentials change
        $this->clear_cache();
    }
}
