<?php

namespace Payop\Database;

/**
 * Database Schema Management
 */
class Schema {
    
    /**
     * Create all plugin tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Create payment groups table
        self::create_payment_groups_table($charset_collate);
        
        // Create payment methods table
        self::create_payment_methods_table($charset_collate);
        
        // Create group methods relationship table
        self::create_group_methods_table($charset_collate);
        
        // Create order data table (HPOS compatible)
        self::create_order_data_table($charset_collate);
        
        // Update database version
        update_option('payop_db_version', PAYOP_VERSION);
    }
    
    /**
     * Create payment groups table
     */
    private static function create_payment_groups_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_payment_groups';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            group_id varchar(50) NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            icon varchar(255),
            grouping_type enum('type', 'country', 'currency', 'custom') DEFAULT 'type',
            enabled tinyint(1) DEFAULT 1,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY group_id (group_id),
            KEY enabled (enabled),
            KEY sort_order (sort_order),
            KEY grouping_type (grouping_type)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
        
        // Insert default groups
        self::insert_default_groups();
    }
    
    /**
     * Create payment methods table
     */
    private static function create_payment_methods_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_payment_methods';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            payop_method_id int(11) NOT NULL,
            title varchar(255) NOT NULL,
            type varchar(50) NOT NULL,
            logo varchar(255),
            currencies text,
            countries text,
            config text,
            group_id varchar(50),
            enabled tinyint(1) DEFAULT 1,
            standalone tinyint(1) DEFAULT 0,
            sort_order int(11) DEFAULT 0,
            last_synced datetime,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY payop_method_id (payop_method_id),
            KEY group_id (group_id),
            KEY enabled (enabled),
            KEY type (type),
            KEY standalone (standalone),
            KEY last_synced (last_synced)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }
    
    /**
     * Create group methods relationship table
     */
    private static function create_group_methods_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_group_methods';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            group_id varchar(50) NOT NULL,
            payop_method_id int(11) NOT NULL,
            sort_order int(11) DEFAULT 0,
            enabled tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY group_method (group_id, payop_method_id),
            KEY group_id (group_id),
            KEY payop_method_id (payop_method_id),
            KEY sort_order (sort_order),
            KEY enabled (enabled)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }
    
    /**
     * Create order data table (HPOS compatible)
     */
    private static function create_order_data_table($charset_collate) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_order_data';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            order_id bigint(20) unsigned NOT NULL,
            payop_invoice_id varchar(255),
            payop_transaction_id varchar(255),
            payop_method_id int(11),
            group_id varchar(50),
            payment_data text,
            status varchar(50) DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY order_id (order_id),
            KEY payop_invoice_id (payop_invoice_id),
            KEY payop_transaction_id (payop_transaction_id),
            KEY payop_method_id (payop_method_id),
            KEY group_id (group_id),
            KEY status (status)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }
    
    /**
     * Insert default payment groups
     */
    private static function insert_default_groups() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'payop_payment_groups';
        
        $default_groups = [
            [
                'group_id' => 'bank_transfer',
                'title' => 'Bank Transfer',
                'description' => 'Pay directly from your bank account',
                'icon' => 'bank-icon.svg',
                'grouping_type' => 'type',
                'sort_order' => 1
            ],
            [
                'group_id' => 'cards_international',
                'title' => 'Credit/Debit Cards',
                'description' => 'Pay with your credit or debit card',
                'icon' => 'card-icon.svg',
                'grouping_type' => 'type',
                'sort_order' => 2
            ],
            [
                'group_id' => 'ewallet',
                'title' => 'Digital Wallets',
                'description' => 'Pay with digital wallet services',
                'icon' => 'wallet-icon.svg',
                'grouping_type' => 'type',
                'sort_order' => 3
            ],
            [
                'group_id' => 'cash',
                'title' => 'Cash Payments',
                'description' => 'Pay at physical locations',
                'icon' => 'cash-icon.svg',
                'grouping_type' => 'type',
                'sort_order' => 4
            ],
            [
                'group_id' => 'crypto',
                'title' => 'Cryptocurrency',
                'description' => 'Pay with cryptocurrency',
                'icon' => 'crypto-icon.svg',
                'grouping_type' => 'type',
                'sort_order' => 5
            ]
        ];
        
        foreach ($default_groups as $group) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE group_id = %s",
                $group['group_id']
            ));
            
            if (!$existing) {
                $wpdb->insert($table_name, $group);
            }
        }
    }
    
    /**
     * Drop all plugin tables
     */
    public static function drop_tables() {
        global $wpdb;
        
        $tables = [
            $wpdb->prefix . 'payop_order_data',
            $wpdb->prefix . 'payop_group_methods',
            $wpdb->prefix . 'payop_payment_methods',
            $wpdb->prefix . 'payop_payment_groups'
        ];
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        delete_option('payop_db_version');
    }
    
    /**
     * Check if tables exist
     */
    public static function tables_exist() {
        global $wpdb;
        
        $tables = [
            $wpdb->prefix . 'payop_payment_groups',
            $wpdb->prefix . 'payop_payment_methods',
            $wpdb->prefix . 'payop_group_methods',
            $wpdb->prefix . 'payop_order_data'
        ];
        
        foreach ($tables as $table) {
            if ($wpdb->get_var("SHOW TABLES LIKE '$table'") !== $table) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get database version
     */
    public static function get_db_version() {
        return get_option('payop_db_version', '0.0.0');
    }
    
    /**
     * Check if database needs update
     */
    public static function needs_update() {
        return version_compare(self::get_db_version(), PAYOP_VERSION, '<');
    }
}
