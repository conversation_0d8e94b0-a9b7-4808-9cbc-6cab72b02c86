<?php

// Test invoice data
$amount = "10.00";
$currency = "EUR";
$id = "test-order-123";
$secretKey = "fd6d7b9d6e14146ba064cd3b7afd7a0e";

// Generate signature: amount:currency:id:secretKey
$data = [$amount, $currency, $id, $secretKey];
$signature = hash('sha256', implode(':', $data));

echo "Signature for test invoice:\n";
echo "Amount: $amount\n";
echo "Currency: $currency\n";
echo "Order ID: $id\n";
echo "Signature: $signature\n";
