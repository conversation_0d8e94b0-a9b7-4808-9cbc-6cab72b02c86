/* PayOp WooCommerce Admin Styles */

/* General <PERSON><PERSON> */
.payop-admin-dashboard,
.payop-gateway-manager {
    margin: 20px 0;
}

.payop-admin-dashboard h1,
.payop-gateway-manager h1 {
    margin-bottom: 30px;
}

/* Status Cards */
.payop-status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: box-shadow 0.3s ease;
}

.status-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-card.status-success {
    border-left: 4px solid #46b450;
}

.status-card.status-error {
    border-left: 4px solid #dc3232;
}

.status-card.status-warning {
    border-left: 4px solid #ffb900;
}

.status-icon {
    font-size: 32px;
    margin-right: 15px;
}

.status-success .status-icon {
    color: #46b450;
}

.status-error .status-icon {
    color: #dc3232;
}

.status-warning .status-icon {
    color: #ffb900;
}

.status-content h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
}

.status-content p {
    margin: 0;
    color: #646970;
    font-size: 14px;
}

/* Gateway Statistics */
.payop-gateway-stats {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.payop-gateway-stats h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #1d2327;
}

.gateway-stats-table {
    overflow-x: auto;
}

.gateway-stats-table table {
    width: 100%;
    border-collapse: collapse;
}

.gateway-stats-table th,
.gateway-stats-table td {
    text-align: left;
    padding: 12px;
    border-bottom: 1px solid #ddd;
}

.gateway-stats-table th {
    background: #f6f7f7;
    font-weight: 600;
}

/* Recent Transactions */
.payop-recent-transactions {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.payop-recent-transactions h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #1d2327;
}

.transactions-table {
    overflow-x: auto;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.status-success {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-warning {
    background: #fff3cd;
    color: #856404;
}

.status-badge.status-error {
    background: #f8d7da;
    color: #721c24;
}

/* System Information */
.payop-system-info {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
}

.payop-system-info h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #1d2327;
}

.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.system-info-section {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
}

.system-info-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1d2327;
    font-size: 16px;
}

.system-info-section ul {
    margin: 0;
    list-style: none;
    padding: 0;
}

.system-info-section li {
    padding: 5px 0;
    border-bottom: 1px solid #ddd;
}

.system-info-section li:last-child {
    border-bottom: none;
}

.system-info-section li.status-success strong {
    color: #46b450;
}

.system-info-section li.status-warning strong {
    color: #ffb900;
}

.system-info-section li.status-error strong {
    color: #dc3232;
}

/* Gateway Manager Specific Styles */
.payop-gateway-overview {
    margin-bottom: 30px;
}

.gateway-overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.overview-card h3 {
    margin: 0 0 5px 0;
    font-size: 28px;
    font-weight: bold;
    color: #2271b1;
}

.overview-card p {
    margin: 0;
    color: #646970;
    font-size: 14px;
}

.card-icon {
    font-size: 36px;
    color: #2271b1;
    opacity: 0.7;
}

.bulk-actions-bar {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.gateway-type {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.gateway-type-standalone {
    background: #e7f3ff;
    color: #0073aa;
}

.gateway-type-grouped {
    background: #fff2cc;
    color: #b85c00;
}

.gateway-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-enabled {
    background: #d4edda;
    color: #155724;
}

.status-disabled {
    background: #f8d7da;
    color: #721c24;
}

.gateway-regions-global {
    color: #2271b1;
    font-weight: 600;
}

.transaction-stats {
    text-align: center;
}

.transaction-count {
    font-weight: bold;
    font-size: 16px;
    color: #1d2327;
}

.gateway-actions {
    white-space: nowrap;
}

.gateway-actions button,
.gateway-actions a {
    margin-right: 5px;
    font-size: 12px;
    padding: 4px 8px;
}

/* Payment Method Logos */
.payop-bank-logos,
.payop-store-logos,
.payop-method-logos {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    margin: 10px 0;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 6px;
}

.bank-logo,
.store-logo,
.method-logo {
    max-height: 32px;
    width: auto;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.more-banks,
.more-stores,
.more-methods {
    background: #2271b1;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* Payment Instructions */
.payop-cash-instructions,
.payop-transfer-options,
.payop-payment-categories {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.payop-cash-instructions h4,
.payop-transfer-options h4,
.payop-payment-categories h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1d2327;
}

.payop-cash-instructions ol {
    margin: 10px 0;
    padding-left: 20px;
}

.payop-cash-expiry {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px 12px;
    margin-top: 10px;
    color: #856404;
}

.transfer-option,
.payment-category {
    margin-bottom: 8px;
    padding: 5px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payop-status-cards,
    .gateway-overview-cards {
        grid-template-columns: 1fr;
    }
    
    .system-info-grid {
        grid-template-columns: 1fr;
    }
    
    .bulk-actions-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .gateway-actions {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .gateway-actions button,
    .gateway-actions a {
        margin-right: 0;
        width: 100%;
        text-align: center;
    }
}

/* Loading States */
.payop-loading {
    opacity: 0.6;
    pointer-events: none;
}

.payop-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notices */
.payop-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    border-radius: 4px;
}

.payop-notice.notice-success {
    background: #d4edda;
    border-color: #46b450;
    color: #155724;
}

.payop-notice.notice-error {
    background: #f8d7da;
    border-color: #dc3232;
    color: #721c24;
}

.payop-notice.notice-warning {
    background: #fff3cd;
    border-color: #ffb900;
    color: #856404;
}

.payop-notice.notice-info {
    background: #d1ecf1;
    border-color: #2271b1;
    color: #0c5460;
}
