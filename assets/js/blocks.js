/**
 * PayOp WooCommerce Blocks Integration
 */

const { registerPaymentMethod } = window.wc.wcBlocksRegistry;
const { getSetting } = window.wc.wcSettings;
const { createElement, Fragment } = window.wp.element;
const { __ } = window.wp.i18n;

// Get PayOp settings
const payopSettings = getSetting('payop_data', {});

// Register PayOp payment methods
Object.keys(payopSettings).forEach(gatewayId => {
    const settings = payopSettings[gatewayId];
    
    registerPaymentMethod({
        name: gatewayId,
        label: settings.title || gatewayId,
        content: createElement(PayOpPaymentMethod, { 
            settings: settings,
            gatewayId: gatewayId 
        }),
        edit: createElement(PayOpPaymentMethod, { 
            settings: settings,
            gatewayId: gatewayId 
        }),
        canMakePayment: () => true,
        ariaLabel: settings.title || gatewayId,
        supports: {
            features: settings.supports || ['products']
        }
    });
});

/**
 * PayOp Payment Method Component
 */
function PayOpPaymentMethod({ settings, gatewayId }) {
    const description = settings.description || '';
    
    return createElement(
        Fragment,
        null,
        description && createElement(
            'div',
            { 
                className: 'payop-payment-description',
                dangerouslySetInnerHTML: { __html: description }
            }
        ),
        createElement(PayOpPaymentFields, { 
            gatewayId: gatewayId,
            settings: settings 
        })
    );
}

/**
 * PayOp Payment Fields Component
 */
function PayOpPaymentFields({ gatewayId, settings }) {
    // This would render any custom fields needed for the payment method
    // For now, we'll return a simple placeholder
    
    if (settings.has_fields) {
        return createElement(
            'div',
            { className: 'payop-payment-fields' },
            createElement(
                'p',
                { className: 'form-row' },
                __('Additional payment details will be collected on the next step.', 'payop-woo')
            )
        );
    }
    
    return null;
}
