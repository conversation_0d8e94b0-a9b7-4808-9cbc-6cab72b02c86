/**
 * PayOp WooCommerce Admin JavaScript
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        PayOpAdmin.init();
    });

    // Main admin object
    window.PayOpAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initComponents();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Dashboard refresh
            $(document).on('click', '.payop-refresh-stats', this.refreshDashboardStats);
            
            // API connection test
            $(document).on('click', '.payop-test-connection', this.testApiConnection);
            
            // Gateway toggle
            $(document).on('click', '.toggle-gateway', this.toggleGateway);
            
            // Bulk actions
            $(document).on('click', '#apply-bulk-action', this.applyBulkAction);
            
            // Select all checkboxes
            $(document).on('change', '#select-all-gateways', this.toggleAllCheckboxes);
            
            // Gateway test
            $(document).on('click', '.test-gateway', this.testGateway);
            
            // Form validation
            $(document).on('submit', '.payop-admin-form', this.validateForm);
        },

        /**
         * Initialize components
         */
        initComponents: function() {
            // Initialize tooltips if available
            if (typeof $.fn.tooltip === 'function') {
                $('[data-toggle="tooltip"]').tooltip();
            }
            
            // Initialize modals if available
            this.initModals();
            
            // Auto-refresh dashboard if enabled
            this.initAutoRefresh();
        },

        /**
         * Refresh dashboard statistics
         */
        refreshDashboardStats: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text(payop_admin.strings.processing).prop('disabled', true);
            
            $.post(payop_admin.ajax_url, {
                action: 'payop_get_dashboard_stats',
                nonce: payop_admin.nonce
            }, function(response) {
                if (response.success) {
                    // Update dashboard stats
                    PayOpAdmin.updateDashboardStats(response.data);
                    PayOpAdmin.showNotice(payop_admin.strings.success, 'success');
                } else {
                    PayOpAdmin.showNotice(response.data.message || payop_admin.strings.error, 'error');
                }
            }).always(function() {
                $button.text(originalText).prop('disabled', false);
            });
        },

        /**
         * Test API connection
         */
        testApiConnection: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text(payop_admin.strings.processing).prop('disabled', true);
            
            $.post(payop_admin.ajax_url, {
                action: 'payop_test_api_connection',
                nonce: payop_admin.nonce
            }, function(response) {
                if (response.success) {
                    PayOpAdmin.showNotice(response.data.message, 'success');
                } else {
                    PayOpAdmin.showNotice(response.data.message || payop_admin.strings.error, 'error');
                }
            }).always(function() {
                $button.text(originalText).prop('disabled', false);
            });
        },

        /**
         * Toggle gateway enabled/disabled
         */
        toggleGateway: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var gatewayId = $button.data('gateway');
            var isEnabled = $button.hasClass('button-primary');
            var originalText = $button.text();
            
            $button.text(payop_admin.strings.processing).prop('disabled', true);
            
            $.post(payop_admin.ajax_url, {
                action: 'payop_toggle_gateway',
                gateway_id: gatewayId,
                enable: !isEnabled,
                nonce: payop_admin.nonce
            }, function(response) {
                if (response.success) {
                    // Refresh the page to show updated status
                    location.reload();
                } else {
                    PayOpAdmin.showNotice(response.data.message || payop_admin.strings.error, 'error');
                    $button.text(originalText).prop('disabled', false);
                }
            });
        },

        /**
         * Apply bulk action to selected gateways
         */
        applyBulkAction: function(e) {
            e.preventDefault();
            
            var action = $('#bulk-action-selector').val();
            var selectedGateways = $('input[name="gateway[]"]:checked').map(function() {
                return this.value;
            }).get();
            
            if (!action || selectedGateways.length === 0) {
                PayOpAdmin.showNotice('Please select an action and at least one gateway.', 'warning');
                return;
            }
            
            if (!confirm(payop_admin.strings.confirm_action)) {
                return;
            }
            
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text(payop_admin.strings.processing).prop('disabled', true);
            
            $.post(payop_admin.ajax_url, {
                action: 'payop_bulk_gateway_action',
                bulk_action: action,
                gateways: selectedGateways,
                nonce: payop_admin.nonce
            }, function(response) {
                if (response.success) {
                    PayOpAdmin.showNotice(response.data.message, 'success');
                    // Refresh the page to show updated status
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    PayOpAdmin.showNotice(response.data.message || payop_admin.strings.error, 'error');
                    $button.text(originalText).prop('disabled', false);
                }
            });
        },

        /**
         * Toggle all checkboxes
         */
        toggleAllCheckboxes: function() {
            var isChecked = $(this).prop('checked');
            $('input[name="gateway[]"]').prop('checked', isChecked);
        },

        /**
         * Test gateway functionality
         */
        testGateway: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var gatewayId = $button.data('gateway');
            var originalText = $button.text();
            
            $button.text(payop_admin.strings.processing).prop('disabled', true);
            
            // Simulate gateway test (in real implementation, this would call actual test)
            setTimeout(function() {
                PayOpAdmin.showNotice('Gateway test completed. Check logs for details.', 'info');
                $button.text(originalText).prop('disabled', false);
            }, 2000);
        },

        /**
         * Validate admin forms
         */
        validateForm: function(e) {
            var $form = $(this);
            var isValid = true;
            
            // Check required fields
            $form.find('[required]').each(function() {
                var $field = $(this);
                if (!$field.val().trim()) {
                    $field.addClass('error');
                    isValid = false;
                } else {
                    $field.removeClass('error');
                }
            });
            
            // Check API credentials if present
            var publicKey = $form.find('[name="payop_public_key"]').val();
            var secretKey = $form.find('[name="payop_secret_key"]').val();
            
            if (publicKey && secretKey) {
                if (publicKey.length < 10 || secretKey.length < 10) {
                    PayOpAdmin.showNotice('API credentials appear to be invalid. Please check your keys.', 'warning');
                }
            }
            
            if (!isValid) {
                e.preventDefault();
                PayOpAdmin.showNotice('Please fill in all required fields.', 'error');
            }
        },

        /**
         * Update dashboard statistics
         */
        updateDashboardStats: function(data) {
            if (data.api_status) {
                $('.api-status-indicator').removeClass('status-success status-error')
                    .addClass(data.api_status.connected ? 'status-success' : 'status-error');
            }
            
            if (data.active_gateways !== undefined) {
                $('.active-gateways-count').text(data.active_gateways);
            }
            
            if (data.pending_orders !== undefined) {
                $('.pending-orders-count').text(data.pending_orders);
            }
            
            if (data.daily_revenue !== undefined) {
                $('.daily-revenue-amount').text(data.daily_revenue);
            }
        },

        /**
         * Initialize modals
         */
        initModals: function() {
            // Create modal container if it doesn't exist
            if (!$('#payop-modal').length) {
                $('body').append('<div id="payop-modal" class="payop-modal"><div class="payop-modal-content"><span class="payop-modal-close">&times;</span><div class="payop-modal-body"></div></div></div>');
            }
            
            // Bind modal events
            $(document).on('click', '.payop-modal-close, .payop-modal', function(e) {
                if (e.target === this) {
                    $('#payop-modal').hide();
                }
            });
            
            // Open modal
            $(document).on('click', '[data-modal]', function(e) {
                e.preventDefault();
                var content = $(this).data('modal');
                PayOpAdmin.openModal(content);
            });
        },

        /**
         * Open modal with content
         */
        openModal: function(content) {
            $('#payop-modal .payop-modal-body').html(content);
            $('#payop-modal').show();
        },

        /**
         * Initialize auto-refresh for dashboard
         */
        initAutoRefresh: function() {
            // Auto-refresh every 5 minutes if on dashboard page
            if ($('.payop-admin-dashboard').length) {
                setInterval(function() {
                    PayOpAdmin.refreshDashboardStats.call($('.payop-refresh-stats')[0], {preventDefault: function(){}});
                }, 300000); // 5 minutes
            }
        },

        /**
         * Show admin notice
         */
        showNotice: function(message, type, dismissible) {
            type = type || 'info';
            dismissible = dismissible !== false;
            
            var noticeClass = 'payop-notice notice-' + type;
            if (dismissible) {
                noticeClass += ' is-dismissible';
            }
            
            var $notice = $('<div class="' + noticeClass + '"><p>' + message + '</p></div>');
            
            // Add dismiss button if dismissible
            if (dismissible) {
                $notice.append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');
            }
            
            // Insert notice
            $('.wrap h1').first().after($notice);
            
            // Auto-dismiss after 5 seconds for non-error notices
            if (type !== 'error' && dismissible) {
                setTimeout(function() {
                    $notice.fadeOut(function() {
                        $(this).remove();
                    });
                }, 5000);
            }
            
            // Scroll to notice
            $('html, body').animate({
                scrollTop: $notice.offset().top - 100
            }, 300);
        },

        /**
         * Format currency
         */
        formatCurrency: function(amount, currency) {
            currency = currency || 'USD';
            
            if (typeof Intl !== 'undefined' && Intl.NumberFormat) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: currency
                }).format(amount);
            }
            
            // Fallback formatting
            return '$' + parseFloat(amount).toFixed(2);
        },

        /**
         * Debounce function
         */
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        /**
         * Utility function to get URL parameter
         */
        getUrlParameter: function(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
    };

    // Global utility functions
    window.payopShowNotice = function(message, type) {
        PayOpAdmin.showNotice(message, type);
    };

    window.payopFormatCurrency = function(amount, currency) {
        return PayOpAdmin.formatCurrency(amount, currency);
    };

})(jQuery);
