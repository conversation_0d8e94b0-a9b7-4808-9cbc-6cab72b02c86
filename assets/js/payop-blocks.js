/**
 * PayOp WooCommerce Blocks Integration
 */

// Import required dependencies
const { registerPaymentMethod } = window.wc.wcBlocksRegistry;
const { createElement, useState, useEffect } = window.wp.element;
const { __ } = window.wp.i18n;

/**
 * PayOp Group Payment Method Component
 */
const PayopGroupPaymentMethod = ({ group_id, group_config }) => {
    const [selectedMethod, setSelectedMethod] = useState('');
    const [dynamicFields, setDynamicFields] = useState([]);
    const [formData, setFormData] = useState({});
    
    const handleMethodSelection = (methodId) => {
        setSelectedMethod(methodId);
        const method = group_config.methods.find(m => m.identifier === methodId);
        setDynamicFields(method ? method.config.fields : []);
        setFormData({});
    };
    
    const handleFieldChange = (fieldName, value) => {
        setFormData(prev => ({
            ...prev,
            [fieldName]: value
        }));
    };
    
    const renderMethodSelection = () => {
        if (group_config.methods.length === 1) {
            // Auto-select single method
            useEffect(() => {
                handleMethodSelection(group_config.methods[0].identifier);
            }, []);
            return null;
        }
        
        return createElement('div', { className: 'payop-method-selection' }, [
            createElement('p', { key: 'title' }, __('Choose your payment method:', 'payop-woo')),
            ...group_config.methods.map(method => 
                createElement('label', {
                    key: method.identifier,
                    className: 'payop-method-option'
                }, [
                    createElement('input', {
                        type: 'radio',
                        name: 'payop_method',
                        value: method.identifier,
                        checked: selectedMethod === method.identifier,
                        onChange: () => handleMethodSelection(method.identifier)
                    }),
                    createElement('div', { className: 'method-info' }, [
                        createElement('img', {
                            src: method.logo,
                            alt: method.title,
                            className: 'method-logo'
                        }),
                        createElement('span', { className: 'method-title' }, method.title)
                    ])
                ])
            )
        ]);
    };
    
    const renderDynamicFields = () => {
        if (!dynamicFields.length) return null;
        
        return createElement('div', { className: 'payop-dynamic-fields' },
            dynamicFields.map(field => renderField(field))
        );
    };
    
    const renderField = (field) => {
        const fieldId = `payop_${field.name}`;
        
        switch (field.type) {
            case 'email':
                return createElement('p', { key: field.name, className: 'form-row' }, [
                    createElement('label', { htmlFor: fieldId }, 
                        field.title || field.name + (field.required ? ' *' : '')
                    ),
                    createElement('input', {
                        type: 'email',
                        id: fieldId,
                        required: field.required,
                        value: formData[field.name] || '',
                        onChange: (e) => handleFieldChange(field.name, e.target.value),
                        className: 'input-text'
                    })
                ]);
                
            case 'text':
                return createElement('p', { key: field.name, className: 'form-row' }, [
                    createElement('label', { htmlFor: fieldId }, 
                        field.title || field.name + (field.required ? ' *' : '')
                    ),
                    createElement('input', {
                        type: 'text',
                        id: fieldId,
                        required: field.required,
                        pattern: field.regexp || undefined,
                        value: formData[field.name] || '',
                        onChange: (e) => handleFieldChange(field.name, e.target.value),
                        className: 'input-text'
                    })
                ]);
                
            default:
                return renderTextField(field);
        }
    };
    
    const renderTextField = (field) => {
        const fieldId = `payop_${field.name}`;
        return createElement('p', { key: field.name, className: 'form-row' }, [
            createElement('label', { htmlFor: fieldId }, 
                field.title || field.name + (field.required ? ' *' : '')
            ),
            createElement('input', {
                type: 'text',
                id: fieldId,
                required: field.required,
                value: formData[field.name] || '',
                onChange: (e) => handleFieldChange(field.name, e.target.value),
                className: 'input-text'
            })
        ]);
    };
    
    return createElement('div', { className: 'payop-group-payment-method' }, [
        renderMethodSelection(),
        renderDynamicFields()
    ]);
};

/**
 * PayOp Individual Payment Method Component
 */
const PayopIndividualPaymentMethod = ({ method_id, method_config }) => {
    const [formData, setFormData] = useState({});
    
    const handleFieldChange = (fieldName, value) => {
        setFormData(prev => ({
            ...prev,
            [fieldName]: value
        }));
    };
    
    const renderFields = () => {
        if (!method_config.config || !method_config.config.fields) {
            return null;
        }
        
        return createElement('div', { className: 'payop-payment-fields' },
            method_config.config.fields.map(field => {
                const fieldId = `payop_${field.name}`;
                
                return createElement('p', { key: field.name, className: 'form-row' }, [
                    createElement('label', { htmlFor: fieldId }, 
                        field.title || field.name + (field.required ? ' *' : '')
                    ),
                    createElement('input', {
                        type: field.type === 'email' ? 'email' : 'text',
                        id: fieldId,
                        required: field.required,
                        pattern: field.regexp || undefined,
                        value: formData[field.name] || '',
                        onChange: (e) => handleFieldChange(field.name, e.target.value),
                        className: 'input-text'
                    })
                ]);
            })
        );
    };
    
    return createElement('div', { className: 'payop-individual-payment-method' }, [
        renderFields()
    ]);
};

// Register payment methods when payopBlocksData is available
if (typeof payopBlocksData !== 'undefined') {
    // Register group payment methods
    if (payopBlocksData.payment_methods.groups) {
        payopBlocksData.payment_methods.groups.forEach(group => {
            registerPaymentMethod({
                name: `payop_group_${group.id}`,
                label: group.title,
                content: createElement(PayopGroupPaymentMethod, {
                    group_id: group.id,
                    group_config: group
                }),
                edit: createElement(PayopGroupPaymentMethod, {
                    group_id: group.id,
                    group_config: group
                }),
                canMakePayment: () => true,
                ariaLabel: group.title,
                supports: {
                    features: ['products']
                }
            });
        });
    }
    
    // Register standalone payment methods
    if (payopBlocksData.payment_methods.standalone) {
        payopBlocksData.payment_methods.standalone.forEach(method => {
            registerPaymentMethod({
                name: `payop_method_${method.identifier}`,
                label: method.title,
                content: createElement(PayopIndividualPaymentMethod, {
                    method_id: method.identifier,
                    method_config: method
                }),
                edit: createElement(PayopIndividualPaymentMethod, {
                    method_id: method.identifier,
                    method_config: method
                }),
                canMakePayment: () => true,
                ariaLabel: method.title,
                supports: {
                    features: ['products']
                }
            });
        });
    }
}
